# 差异化静态IP配置逻辑实现方案

## 📋 需求总结

实现差异化的静态IP配置逻辑：
- **上网网关接口**：需要完整配置（IP + 子网掩码 + 网关）
- **非上网接口**：只需基础配置（IP + 子网掩码）
- **判定条件**：放在代码最上面，区分不同接口的配置要求

## 💡 实现方案

### **完整代码实现**

```c
// ============================================================================
// 差异化静态IP配置条件判定逻辑（放在最上面）
// ============================================================================

// 上网网卡才判定网关 否则不判定
INT32 gateway_need = FALSE;
if (strlen(g_manual_gateway_interface) > 0) {
    if (stricmp(if_name, g_manual_gateway_interface) EQU 0) {
        gateway_need = TRUE;
    }
}

// 差异化静态IP配置条件判定
UINT8 should_configure_static_ip = FALSE;

if (!net->dhcp && strlen(net->ip) > 0 && strlen(net->netmask) > 0) {
    if (gateway_need) {
        // 上网网关接口：必须有完整配置（包括网关）
        if (strlen(net->gateway) > 0) {
            should_configure_static_ip = TRUE;
            LOGI("Gateway interface %s: Complete static IP configuration enabled", if_name);
        } else {
            should_configure_static_ip = FALSE;
            LOGW("Gateway interface %s: Missing gateway configuration, static IP disabled", if_name);
        }
    } else {
        // 非上网接口：只需基础配置（不需要网关）
        should_configure_static_ip = TRUE;
        LOGI("Non-gateway interface %s: Basic static IP configuration enabled", if_name);
    }
} else {
    should_configure_static_ip = FALSE;
    if (net->dhcp) {
        LOGI("Interface %s: DHCP mode enabled, static IP disabled", if_name);
    } else {
        LOGW("Interface %s: Incomplete static IP configuration (missing IP or netmask)", if_name);
    }
}

LogW("Interface: %s, gateway_need: %d, dhcp: %d, static_ip_enabled: %d", 
     if_name, gateway_need, net->dhcp, should_configure_static_ip);

// ============================================================================
// 静态IP配置实现逻辑
// ============================================================================

if (should_configure_static_ip) {
    // 记录配置信息
    LOGI("Configuring static IP for %s: ip[%s], netmask[%s], gateway[%s], gateway_need=%d", 
         if_name, net->ip, net->netmask, 
         strlen(net->gateway) > 0 ? net->gateway : "none", gateway_need);

    // 基础静态IP配置（所有静态IP接口都需要）
    fprintf(js_file, "# Static IP configuration for %s\n", if_name);
    fprintf(js_file, "echo 'Configuring static IP for %s: %s/%s'\n", if_name, net->ip, net->netmask);
    fprintf(js_file, "ifconfig %s %s netmask %s\n", if_name, net->ip, net->netmask);

    // 网关配置（只有上网接口才设置默认网关）
    if (gateway_need && strlen(net->gateway) > 0) {
        // 上网网关接口：设置默认网关
        fprintf(js_file, "# Setting default gateway for gateway interface %s\n", if_name);
        fprintf(js_file, "route add default gw %s dev %s 2>/dev/null\n", net->gateway, if_name);
        fprintf(js_file, "echo 'Default gateway set for %s: %s (designated gateway interface)'\n", 
                if_name, net->gateway);
        
        LOGI("Default gateway configured for %s: %s", if_name, net->gateway);
    } else if (strlen(net->gateway) > 0) {
        // 非上网接口：不设置默认网关，但记录网关信息
        fprintf(js_file, "# Gateway %s not set for %s (not designated gateway interface)\n", 
                net->gateway, if_name);
        fprintf(js_file, "echo 'Gateway skipped for %s: %s (not designated gateway interface)'\n", 
                if_name, net->gateway);
        
        LOGI("Gateway skipped for %s: %s (not designated gateway interface)", if_name, net->gateway);
    } else {
        // 非上网接口且无网关配置
        fprintf(js_file, "# No gateway configuration for non-gateway interface %s\n", if_name);
        fprintf(js_file, "echo 'No gateway configured for %s (non-gateway interface)'\n", if_name);
        
        LOGI("No gateway configured for %s (non-gateway interface)", if_name);
    }

    // 复制配置文件
    fprintf(js_file, "cp -f %s /tmp/\n", CFG_NETWORK(CFG_PATH, is_eth1));
    LogE("SAVE:: if_name = %s, file = %s, ip = %s", if_name, CFG_NETWORK(CFG_PATH, is_eth1), net->ip);

    // DNS配置（所有静态IP接口都配置DNS）
    {
        // DNS配置逻辑
        CHAR dns_content[1024] = {0};
        CHAR existing_dns[1024] = {0};

        // 读取现有的DNS配置
        FILE *existing_file = fopen("/tmp/resolv.conf", "rb");
        if (existing_file) {
            fread(existing_dns, 1, sizeof(existing_dns)-1, existing_file);
            fclose(existing_file);
        }

        // 构建当前接口的DNS配置
        if (net->dns[0][0]) {
            // 有配置DNS
            snprintf(dns_content, sizeof(dns_content), "# DNS for %s\nnameserver %s\n", if_name, net->dns[0]);
            if (net->dns[1][0]) {
                CHAR temp[256];
                snprintf(temp, sizeof(temp), "nameserver %s\n", net->dns[1]);
                strncat(dns_content, temp, sizeof(dns_content) - strlen(dns_content) - 1);
            }
        } else {
            // 未配置DNS，使用默认
            snprintf(dns_content, sizeof(dns_content),
                "# Default DNS for %s\n"
                "nameserver 114.114.114.114\n"
                "nameserver 223.5.5.5\n"
                "nameserver 8.8.8.8\n", if_name);
        }

        // 合并DNS配置（避免重复）
        FILE *pfile = fopen("/tmp/resolv.conf", "wb");
        if (pfile) {
            // 写入现有的其他接口DNS配置（如果不冲突）
            if (strlen(existing_dns) > 0 && !strstr(existing_dns, dns_content)) {
                fprintf(pfile, "%s", existing_dns);
            }
            // 写入当前接口的DNS配置
            fprintf(pfile, "%s", dns_content);
            fflush(pfile);
            fclose(pfile);

            LOGI("DNS configured for %s independently", if_name);
        }
    }

    // 配置完成标记
    fprintf(js_file, "echo 'Static IP configuration completed for %s'\n", if_name);
    
} else {
    // 进入DHCP配置逻辑
    char dhcpc_cfg[64];

    LOGI("DHCP configuration for %s", if_name);
    strcpy(dhcpc_cfg, "/opt/dhcpc.conf");

    // DHCP配置逻辑
    fprintf(js_file, "# DHCP configuration for %s\n", if_name);
    fprintf(js_file, "echo 'Starting DHCP for %s'\n", if_name);
    fprintf(js_file, "DHCP_GATEWAY_ALLOWED=1\n");

    // 停止现有DHCP客户端
    fprintf(js_file, "killall -9 udhcpc 1>/dev/null 2>&1\n");

    // 其他DHCP配置逻辑...
    // （这里继续原有的DHCP配置代码）
}
```

## 🎯 关键特性说明

### **1. 判定条件在最上面**
```c
// 所有判定逻辑都在代码最上面完成
INT32 gateway_need = FALSE;
UINT8 should_configure_static_ip = FALSE;

// 完成所有判定后，再进入具体的配置逻辑
if (should_configure_static_ip) {
    // 静态IP配置
} else {
    // DHCP配置
}
```

### **2. 差异化配置条件**
```c
if (gateway_need) {
    // 上网网关接口：需要完整配置
    if (strlen(net->gateway) > 0) {
        should_configure_static_ip = TRUE;
    }
} else {
    // 非上网接口：只需基础配置
    should_configure_static_ip = TRUE;
}
```

### **3. 网关设置逻辑**
```c
if (gateway_need && strlen(net->gateway) > 0) {
    // 只有上网接口才设置默认网关
    fprintf(js_file, "route add default gw %s dev %s 2>/dev/null\n", net->gateway, if_name);
} else {
    // 非上网接口不设置默认网关
    fprintf(js_file, "echo 'Gateway skipped for %s (not designated gateway interface)'\n", if_name);
}
```

## 📊 配置逻辑对比

| 接口类型 | 配置条件 | IP配置 | 网关配置 | DNS配置 |
|----------|----------|--------|----------|---------|
| 上网接口 | dhcp=0 + IP + 掩码 + **网关** | ✅ | ✅ 设置默认网关 | ✅ |
| 非上网接口 | dhcp=0 + IP + 掩码 | ✅ | ❌ 不设置默认网关 | ✅ |

## 🔍 日志输出示例

### **上网接口配置**:
```
Gateway interface eth0: Complete static IP configuration enabled
Configuring static IP for eth0: ip[*************], netmask[*************], gateway[***********], gateway_need=1
Default gateway configured for eth0: ***********
```

### **非上网接口配置**:
```
Non-gateway interface eth1: Basic static IP configuration enabled
Configuring static IP for eth1: ip[*************], netmask[*************], gateway[none], gateway_need=0
No gateway configured for eth1 (non-gateway interface)
```

## ✅ 实现优势

1. **条件判定清晰**：所有判定逻辑在最上面，易于理解和维护
2. **差异化配置**：根据接口角色采用不同的配置要求
3. **避免路由冲突**：只有一个接口设置默认网关
4. **完整日志记录**：详细记录配置过程和决策依据
5. **兼容性好**：与现有代码结构完全兼容

## 🚀 使用说明

1. **替换现有条件判断**：将原有的静态IP判断条件替换为上述代码
2. **保持现有逻辑**：DHCP配置逻辑保持不变
3. **测试验证**：分别测试上网接口和非上网接口的静态IP配置
4. **日志监控**：通过日志确认配置逻辑的正确执行

这个实现方案完全满足您的需求，确保了差异化的静态IP配置逻辑，同时保持代码结构的清晰性和可维护性。

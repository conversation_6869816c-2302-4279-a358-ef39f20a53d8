#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <dirent.h>
#include <unistd.h>
#include <sys/stat.h>
#include <sys/statvfs.h>
#include <sys/statfs.h>
#include <stdio.h>
#include <errno.h>
#include <pthread.h>
#include <signal.h>

#include <sys/wait.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>


// 最多7个硬盘
#define MAX_DISK_COUNT  7			


// 磁盘结构体
typedef struct {
    char *device;     // 设备路径，例如 /dev/sda1
    char *mountpoint; // 挂载路径，例如 /mnt/disk0
} DISK_INFO;



// 挂载信息结构
typedef struct {
    char *device;     // 设备路径，例如 /dev/sda1
    char *mountpoint; // 挂载路径，例如 /mnt/disk0
} MOUNT_INFO;

// 定义挂载映射关系
#define DISK_MAPPING { \
    { (char *)"/dev/sda1", (char *)"/mnt/disk1" }, \
    { (char *)"/dev/sdb1", (char *)"/mnt/disk2" }, \
    { (char *)"/dev/sdc1", (char *)"/mnt/disk3" }, \
    { (char *)"/dev/sdd1", (char *)"/mnt/disk4" }, \
    { (char *)"/dev/sde1", (char *)"/mnt/disk5" }, \
    { (char *)"/dev/sdf1", (char *)"/mnt/disk6" }, \
    { (char *)"/dev/sdg1", (char *)"/mnt/disk7" }  \
}


#define SECTOR_SIZE 512


#ifndef EQU
#define EQU ==
#endif

#define SDK_ERROR(fd, fn) if(fd != 0) fn

#ifndef FAIL
#define FAIL (-1)
#endif

#ifndef OK
#define OK (0)
#endif


#define	MOUNT_PATH	"/mnt/disk1"

/**
 * 释放缓存
 */
void system_free_cache()
{
	int		fd;
	char	val = '3';
	
	sync(); // 同步磁盘数据,将缓存数据回写到硬盘,以防数据丢失
	fd = open("/proc/sys/vm/drop_caches", O_WRONLY|O_CREAT,	0644);
	if (fd EQU -1) {
		printf("%s() open(/proc/sys/vm/drop_caches) fail, errno=%d\n\n", __func__,  errno);
		return;
	}

	write(fd, &val, 1);
	close(fd);
}


static int my_system(const char *command)
{
	int 	i;
    int 	pid = 0;
    int 	status = 0;
    extern char **environ;
    
    if (NULL EQU command) {
        return 1;
    }
    
    pid = vfork();        /* vfork() also works */
    if (pid < 0) {
        status = -1;
    }
    else if (0 EQU pid) {             /* child process */

		// 关闭所有父进程句柄
		for (i = 3; i <sysconf(_SC_OPEN_MAX); i++)  
      		close(i);
		
        execle("/bin/sh", "sh", "-c", command, NULL, environ);    /* execve() also an implementation of exec() */
        exit(127);
    } 
	else {

	    /* wait for child process to start */
	    while (waitpid(pid, &status, 0) < 0) {
	        if (errno != EINTR) {
	            status = -1;
				break;
	        }
	    }
	}
    
    return status;
}


/**
 * 等同于system函数,只不过错误有信息打印
 */
int system_run(const char *cmd)
{
	typedef void (*sighandler_t)(int);	
	sighandler_t 	old_handler;  
	int 			ret;

	system_free_cache();
	old_handler = signal(SIGCHLD, SIG_DFL);  
	//ret = system(cmd);
	ret = my_system(cmd);
	if (ret < 0) {
		printf("\033[40;31m" "system_run() error, code=%d; cmd: \n%s\n" "\033[0m", errno, cmd);
	}
	signal(SIGCHLD, old_handler);  
	
	return ret;
}

/**
 * 等同于system函数[不继承父进程句柄],只不过错误有信息打印
 */
int system_no_fd(const char *cmd)
{
	typedef void (*sighandler_t)(int);	
	sighandler_t 	old_handler;  
	int 			ret;

	system_free_cache();
	old_handler = signal(SIGCHLD, SIG_DFL);  
	ret = my_system(cmd);
	if (ret < 0) {
		printf("\033[40;31m" "system_no_fd() error, code=%d; cmd: \n%s\n" "\033[0m", errno, cmd);
	}
	signal(SIGCHLD, old_handler);  
	
	return ret;
}





int is_disk_device(const char *name) 
{
    return (
        strncmp(name, "sd", 2) == 0 ||
        strncmp(name, "nvme", 4) == 0 ||
        strncmp(name, "hd", 2) == 0
    );
}

// 列出所有磁盘
void list_all_disks() 
{
    struct dirent *entry;
    DIR *dp = opendir("/sys/block");

    if (!dp) {
        perror("opendir");
        return;
    }

    printf("可用磁盘设备：\n");
    while ((entry = readdir(dp))) {
        if (entry->d_type == DT_LNK && is_disk_device(entry->d_name)) {
            char size_path[128];
            char size_buf[64];
            snprintf(size_path, sizeof(size_path), "/sys/block/%s/size", entry->d_name);

            FILE *fp = fopen(size_path, "r");
            if (!fp) continue;
            if (fgets(size_buf, sizeof(size_buf), fp)) {
                long long sectors = atoll(size_buf);
                long long bytes = sectors * SECTOR_SIZE;
                printf("  /dev/%s  (%.2f GB)\n", entry->d_name, bytes / 1e9);
            }
            fclose(fp);
        }
    }

    closedir(dp);
}


void show_disk_details(const char *devname) 
{
    char path[128];
    char vendor[64] = "未知";
    char model[64] = "未知";
    unsigned long long size_bytes = 0;
    unsigned int block_size = 0;
    unsigned long long avail_bytes = 0;

    // 去掉 /dev/ 前缀，只保留设备名，例如 sda
    const char *dev = strrchr(devname, '/');
    if (!dev || strlen(dev) < 2){
		printf("dev = %s\n", dev);
		return;
	}
	
    dev++;  // skip '/'

	printf("dev = %s\n", dev);

    // 1. 获取厂商和型号
    snprintf(path, sizeof(path), "/sys/block/%s/device/vendor", dev);
    FILE *f = fopen(path, "r");
    if (f) {
        fgets(vendor, sizeof(vendor), f);
        fclose(f);
    }

    snprintf(path, sizeof(path), "/sys/block/%s/device/model", dev);
    f = fopen(path, "r");
    if (f) {
        fgets(model, sizeof(model), f);
        fclose(f);
    }

    // 去除换行
    vendor[strcspn(vendor, "\n")] = 0;
    model[strcspn(model, "\n")] = 0;

    // 2. 获取容量（总字节）
    snprintf(path, sizeof(path), "/sys/block/%s/size", dev);
    f = fopen(path, "r");
    if (f) {
        unsigned long long sectors = 0;
        fscanf(f, "%llu", &sectors);
        size_bytes = sectors * 512;
        fclose(f);
    }

    // 3. 获取块大小
    snprintf(path, sizeof(path), "/sys/block/%s/queue/logical_block_size", dev);
    f = fopen(path, "r");
    if (f) {
        fscanf(f, "%u", &block_size);
        fclose(f);
    }
	
	printf("vendor = %s, model = %s, block_size = %d\n", vendor, model, block_size);

}


// 判断是否是分区（如 /dev/sda1），是则返回 TRUE，否则返回 FALSE
// 判断是否是分区（如 /dev/sda1），是则返回 1，否则返回 0
int is_partition(const char *dev) {
    if (!dev) return 0; // 防止空指针
    size_t len = strlen(dev);
    // "/dev/sda1" 最短长度为9
    if (len < 9) return 0;
    if (strncmp(dev, "/dev/sd", 7) != 0)
        return 0;
    if (dev[7] < 'a' || dev[7] > 'z')
        return 0;
    // 第8位及以后必须全为数字，且至少有一位数字
    for (size_t i = 8; i < len; ++i) {
        if (dev[i] < '0' || dev[i] > '9')
            return 0;
    }
    return 1;
}




// 生成第一个分区名，如 /dev/sda -> /dev/sda1
void get_first_partition(const char *dev, char *out, size_t out_size) {
    snprintf(out, out_size, "%s1", dev);
}

// 获取挂载点
char* get_mount_point_from_device(const char *part_dev) 
{
    MOUNT_INFO disks[MAX_DISK_COUNT] = DISK_MAPPING;
	
    for (int i = 0; i < MAX_DISK_COUNT; ++i) {
        if (strcmp(part_dev, disks[i].device) == 0) {
			printf("device = %s, mountpoint = %s\n", disks[i].device, disks[i].mountpoint);
            return disks[i].mountpoint;
        }
    }

    return NULL; // 未找到
}



// mount 
int mount_disk(const char *device) 
{	
	int  ret = 0;
	char cmd[256];
	char part_dev[128];		// 挂载设备分区 /dev/sda1
	char part_name[128];		// 分区名		/dev/sda1
	char *mountpoint = NULL;

	memset(part_dev, 0,  sizeof(part_dev));
	memset(part_name, 0,  sizeof(part_name));


	printf("device = %s\n", device);
	
	// 是否是分区
    if (!is_partition(device)) {
        get_first_partition(device, part_name, sizeof(part_name));
		strcpy(part_dev, part_name);
    }
	else {
		strcpy(part_dev, device);
	}

	printf("part_dev = %s, part_name = %s\n", part_dev);
	
	mountpoint = get_mount_point_from_device(part_dev);
	if (!mountpoint){
		printf("mountpoint is NULL, part_dev = %s\n", part_dev);
		return FAIL;
	}

	printf("part_dev = %s, mountpoint = %s\n", part_dev, mountpoint);

	// 卸载所有挂载在该磁盘下的分区
	memset(cmd, 0, sizeof(cmd));
	snprintf(cmd, sizeof(cmd), "mount | grep %s | awk '{print $3}' | xargs -r -n1 umount", device);
	system_no_fd(cmd);

    // 创建挂载目录
	memset(cmd, 0, sizeof(cmd));
    snprintf(cmd, sizeof(cmd), "mkdir -p %s", mountpoint);
    system_no_fd(cmd);

    // 执行挂载
    memset(cmd, 0, sizeof(cmd));
    snprintf(cmd, sizeof(cmd), "mount -t ext4 -o rw,noatime,nodiratime %s %s", part_dev, mountpoint);
	printf("\ncmd = %s\n", cmd);

	ret = system_no_fd(cmd);
    if (ret == 0) {
        printf("挂载成功:: %s -> %s\n", part_dev, mountpoint);
    } 
	else {
        printf("挂载失败:: %s -> %s, 返回码 %d\n", part_dev, mountpoint, ret);
    }

	return OK;
}



// 使用 parted 分区 传入 /dev/sda 或 /dev/sdb 
int vs_delete_format_part(const char *dev_disk) 
{
	int ret = 0;
	char cmd[256];
	char part[256];
	
	// 检测参数
    FILE *fp = fopen(dev_disk, "r");
    if (!fp) {
        perror("fopen");
		printf("打开失败 dev_disk = %s\n", dev_disk);
        return FAIL;
    }
	fclose(fp);

	memset(part, 0, sizeof(part));
	snprintf(part, sizeof(part), "%s1", dev_disk);	// /dev/sda1

	// 卸载所有挂载在该磁盘下的分区
    memset(cmd, 0, sizeof(cmd));
    snprintf(cmd, sizeof(cmd), "mount | grep %s | awk '{print $3}' | xargs -r -n1 umount", dev_disk);
	printf("卸载分区:: cmd = %s\n", cmd);
    ret = system_no_fd(cmd);
	if (ret == 0) {
        printf("卸载分区 成功!! \n");
    } 
	else {
        printf("卸载分区 失败!! 返回码 %d\n", ret);
    }
#if 0
	printf("创建 分区 开始 (%s)...\n", dev_disk);
	memset(cmd, 0, sizeof(cmd));
	snprintf(cmd, sizeof(cmd), "dd if=/dev/zero of=%s bs=1M count=10", dev_disk);
	system_no_fd(cmd);

 	// 创建 GPT 分区表（会清除原有所有分区）
 	memset(cmd, 0, sizeof(cmd));
    snprintf(cmd, sizeof(cmd), "/tmp/tools/parted -s %s mklabel gpt", dev_disk);
 	printf("\ncmd = %s\n", cmd);
	ret = system_no_fd(cmd);
	if (ret != 0) {
		printf("创建 GPT 分区表 失败!! 返回码 %d\n", ret);
	} 
	else {
		printf("创建 GPT 分区表 成功...\n");
		sleep(2);
	}

	// 让内核刷新分区表
	memset(cmd, 0, sizeof(cmd));
	snprintf(cmd, sizeof(cmd), "partprobe %s", dev_disk);
	system_no_fd(cmd);

	// 创建分区表 (一个硬盘一个) 注意 %% 
	memset(cmd, 0, sizeof(cmd));
	snprintf(cmd, sizeof(cmd), "/tmp/tools/parted --align=opt -s %s unit MiB mkpart primary ext4 1 100%%", dev_disk);
	printf("\ncmd = %s\n", cmd);
	ret = system_no_fd(cmd);
    if (ret != 0) {
        printf("创建 一个总分区 失败!! 返回码 %d\n", ret);
    } 
	else {
        printf("创建 一个总分区 成功!! ...\n");
        sleep(2);
    }

	// 让内核刷新分区表
	memset(cmd, 0, sizeof(cmd));
	snprintf(cmd, sizeof(cmd), "partprobe %s", dev_disk);
	system_no_fd(cmd);

	printf("创建 分区 成功(%s)...\n", dev_disk);

	// 格式化新分区
    printf("准备格式化 ---- \n");
	memset(cmd, 0, sizeof(cmd));
    snprintf(cmd, sizeof(cmd), "/tmp/tools/mkfs.ext4 -F -t ext4 -v -m 0 -b 4096 -i 67108864 -T huge %s 2>&1", part);

	printf("cmd = %s \n\n", cmd);
	
    ret = system_no_fd(cmd);
	if (ret == 0) {
        printf("格式化 成功!! \n");
    } 
	else {
        printf("格式化 失败!! 返回码 %d\n", ret);
    }
#endif
	return ret;
}


// 删除所有分区 并格式化
int vs_format_disk(const char *disk) 
{
	char part[64];
	int ret = 0;
	char cmd[256];
	
    if (strstr(disk, "/dev/") != disk) {
        fprintf(stderr, "❌ 非法磁盘路径：%s\n", disk);
        return FAIL;
    }
    
	// 重新分区并格式化    
	ret = vs_delete_format_part(disk);
	if (ret != OK){
		printf("vs_delete_format_part failed!");
		return ret;
	}
	
	// 挂载
    ret = mount_disk(disk);
	if (ret != OK){
		printf("mount_disk failed!");
		return ret;
	}

	return ret;
}


void print_menu() 
{
    printf("\n======= 硬盘管理工具 =======\n");
    printf("1. 列出所有磁盘\n");
    printf("2. 查看磁盘详细信息\n");
    printf("3. 查看容量\n");
	printf("4. 格式化\n");
    printf("0. 退出\n");
    printf("请输入选项：");
}

// 扫描并挂载硬盘
void scan_and_mount() 
{
    DIR *dir = opendir("/dev");
    struct dirent *entry;
    char devpath[256];
    char mountpoint[256];
	int  ret = 0;
	
    if (!dir) {
        perror("opendir /dev");
        return;
    }

    while ((entry = readdir(dir)) != NULL) {

		// 块设备 硬盘
		if (entry->d_type == DT_BLK && is_disk_device(entry->d_name)) {
	        snprintf(devpath, sizeof(devpath), "/dev/%s", entry->d_name);

			// /dev/sda1 等分区文件才能挂载
	        if (!is_partition(devpath)) {
	            ret = mount_disk(devpath);
				if (ret != 0){
					printf("挂载失败,需要格式化! devpath = %s\n", devpath);
				}
				else{
					printf("挂载成功! devpath = %s\n", devpath);
				}
	        }
		}
    }
	
    closedir(dir);
}


// 遍历获取容量
void get_disk_capacity() 
{
	struct statvfs vfs;
	MOUNT_INFO disks[MAX_DISK_COUNT] = DISK_MAPPING;
	
	for (int i = 0; i < MAX_DISK_COUNT; i++)
	{
	    memset(&vfs, 0, sizeof(struct statvfs));
		
	    if (statvfs(disks[i].mountpoint, &vfs) != 0) {
	        perror("statvfs");
	        continue;
	    }

	    u_int64_t block_size = vfs.f_frsize;
	    u_int64_t total_blocks = vfs.f_blocks;
	    u_int64_t avail_blocks = vfs.f_bavail;

	    u_int64_t total_bytes = block_size * total_blocks;
	    u_int64_t avail_bytes = block_size * avail_blocks;

		printf("设备路径    : %s\n", disks[i].device);
	    printf("挂载路径    : %s\n", disks[i].mountpoint);
	    printf("总容量      : %.2f GB\n", total_bytes / 1024.0 / 1024 / 1024);
	    printf("可用容量    : %.2f GB\n", avail_bytes / 1024.0 / 1024 / 1024);
	}
}

int main() {
    int choice;
    char devname[64];

    while (1) {
        print_menu();
        if (scanf("%d", &choice) != 1) {
            while (getchar() != '\n');  // 清空输入缓冲区
            continue;
        }

        switch (choice) {
			
            case 1:
				printf("列出识别到的硬盘!!\n");
                list_all_disks();
                break;
            case 2:
                printf("显示磁盘型号 /dev/sda/ \n");
                show_disk_details("/dev/sda/");
                break;
			case 3:
				printf("显示磁盘容量 /dat/sda1 \n");
				get_disk_capacity();
				break;
            case 4:
                printf("删除所有分区并格式化磁盘 /dev/sda \n");
                scanf("%s", devname);
                vs_format_disk(devname);
				
                break;
			case 5:
				printf("扫描并挂载!!\n");
				scan_and_mount();
				break;
            case 0:
                printf("退出程序。\n");
                return 0;
            default:
                printf("无效选项。\n");
                break;
        }
    }

    return 0;
}

#include <math.h>
#include "low_delay_common.h"

//#define	BT_6WND

//==============================================================================
// 计算行数
//==============================================================================
static
int CalcRow(int iNums)
{
    int iRow;

    /**** 计算行列 ****/
	switch (iNums)
	{
	case 1:
		iRow = 1;
		break;

	case 2:
	case 3:
	case 4:
		iRow = 2;
		break;

#ifndef BT_6WND
  	case 6:
#endif	
	case 5:
    case 9:
		iRow = 3;
		break;

	case 7:
    case 8:
	case 10:
	case 11:
	case 12:
	case 13:
	case 14:
	case 15:
	case 16:
		iRow = 4;
		break;

	case 17:
	case 19:
	case 21:
	case 22:
	case 23:
	case 24:
	case 25:
#ifdef BT_6WND
    case 6:
#endif
		iRow = 5;
		break;

	case 18:
	case 20:
		iRow = 6;
		break;

	default:
		iRow = (int)sqrt((float)iNums);
		if (iNums - iRow*iRow>0)
			iRow ++;
		break;
	}

    return iRow;
}

//==============================================================================
// 计算显示的窗口个数
//==============================================================================

// hcc 处理警告 删除 
//static
//int CalcShowNums(int iNums)
//{
//    int iRow = CalcRow(iNums);
//
//    return iRow * iRow;
//}
#define ALIGN2_BACK(x, a)              ((a) * (((x) / (a))))

//==============================================================================
// 计算区域坐标及大小与剩余个数（请保证pAreaRect的数量是CalcShowNums(iNums)）
//==============================================================================
static 
int CalcViewAreaAndRemain(MY_RECT *pAreaRect, const MY_RECT &tWorkRect,
				  			int iNums, int *piRemain, int iRowSpace = 1)
{
#define SetRemain(nn) if (piRemain != NULL) *piRemain = (nn); 
	int i,j;
	int iRow = CalcRow(iNums); 	// 行列
	int iWidth, iHeight;
	MY_RECT *pRect;	// 存存储排放区域

	if (pAreaRect==NULL)
	{
		return FALSE;
	}  

	/*** 开辟暂存区域的内存 ***/
	pRect 	= new MY_RECT[iRow * iRow];
	iWidth 	= (tWorkRect.right - tWorkRect.left) / iRow - iRowSpace;
	iHeight = (tWorkRect.bottom - tWorkRect.top) / iRow - iRowSpace;
	for (i=0; i<iRow; i++)
	{
		for (j=0; j<iRow; j++)
		{
            MY_RECT   &cur = pRect[i * iRow + j];

			cur.top   	= ALIGN2_BACK(tWorkRect.top + i * iHeight + iRowSpace, 2);
			cur.left 	= ALIGN2_BACK(tWorkRect.left + j * iWidth + iRowSpace, 2);
			cur.bottom 	= ALIGN2_BACK(tWorkRect.top + (i + 1) * iHeight, 2);
			cur.right 	= ALIGN2_BACK(tWorkRect.left + (j + 1) * iWidth, 2);

			if (j == iRow - 1)
			{
				cur.right = ALIGN2_BACK(tWorkRect.right - iRowSpace, 2);
			}

			if (i == iRow-1)
			{
				cur.bottom = ALIGN2_BACK(tWorkRect.bottom - iRowSpace, 2);
			}
		}
	}

	/*** 整理区域 ***/
	switch (iNums)
	{
	case 5:
	case 6:
		pRect[0].right = pRect[4].right;
		pRect[0].bottom = pRect[4].bottom;
		pRect[1] = pRect[2];
		pRect[2] = pRect[5];
		pRect[3] = pRect[6];
		pRect[4] = pRect[7];
        if (iNums == 5 && piRemain == NULL)
        {
        }
        else
        {
			pRect[5] = pRect[8];
        }
        SetRemain(6 - iNums);
		break;

//    case 6:
//#ifdef BT_6WND
//		pRect[0].right = pRect[23].right;
//		pRect[0].bottom = pRect[23].bottom;
//		pRect[1] = pRect[4];
//		pRect[2] = pRect[9];
//		pRect[3] = pRect[14];
//        pRect[4] = pRect[19];
//        pRect[5] = pRect[24];
//        SetRemain(0);
//#else
//    	pRect[0].right = pRect[5].right;
//		pRect[0].bottom = pRect[5].bottom;
//		pRect[1] = pRect[2];
//		pRect[1].right = pRect[7].right;
//		pRect[1].bottom = pRect[7].bottom;
//		for (i=2; i<6; i++)
//		{
//			pRect[i] = pRect[i + 6];
//		}
////        iHeight = (pRect[5].bottom - pRect[5].top);
////        for (i=0; i<2; i++)
////        {
////			pRect[i].bottom += iHeight;
////        }
//        iHeight = (pRect[5].bottom - pRect[5].top)/2;
//        for (i=0; i<6; i++)
//        {
//        	pRect[i].top += iHeight;
//			pRect[i].bottom += iHeight;
//        }
//        SetRemain(0);
//#endif
//		break;

	case 7:
		pRect[0].right = pRect[5].right;
		pRect[0].bottom = pRect[5].bottom;
		pRect[1] = pRect[2];
		pRect[1].right = pRect[7].right;
		pRect[1].bottom = pRect[7].bottom;
		pRect[2] = pRect[8];
		pRect[2].right = pRect[13].right;
		pRect[2].bottom = pRect[13].bottom;
		pRect[3] = pRect[10];
		pRect[4] = pRect[11];
		pRect[5] = pRect[14];
		pRect[6] = pRect[15];
        SetRemain(0);
		break;

    case 8:
		pRect[0].right = pRect[10].right;
		pRect[0].bottom = pRect[10].bottom;
		pRect[1] = pRect[3];
        pRect[2] = pRect[7];
        pRect[3] = pRect[11];
        pRect[4] = pRect[12];
        pRect[5] = pRect[13];
		pRect[6] = pRect[14];
		pRect[7] = pRect[15];

        SetRemain(0);
		break;

	case 10:
		pRect[0].right = pRect[5].right;
		pRect[0].bottom = pRect[5].bottom;
		pRect[1] = pRect[2];
		pRect[1].right = pRect[7].right;
		pRect[1].bottom = pRect[7].bottom;
		for (i=2; i<10; i++)
		{
			pRect[i] = pRect[i + 6];
		}
        SetRemain(0);
		break;

	case 11:
	case 12:
	case 13:
		pRect[5].right = pRect[6].right;
		pRect[5].bottom = pRect[9].bottom;
        pRect[6] = pRect[7];
        pRect[7] = pRect[8];
		for (i=8; i<(piRemain == NULL ? iNums : 13); i++)
		{
			pRect[i] = pRect[i + 3];
		}
        SetRemain(13-iNums);
		break;

	case 17:
		pRect[0].right = pRect[12].right;
		pRect[0].bottom = pRect[12].bottom;
		pRect[1] = pRect[3];
		pRect[2] = pRect[4];
		pRect[3] = pRect[8];
		pRect[4] = pRect[9];
		pRect[5] = pRect[13];
		pRect[6] = pRect[14];
		for (i=7; i<17 ;i++)
		{
			pRect[i] = pRect[i + 8];
		}
        SetRemain(0);
		break;

	case 18:
		for (i=0; i<6; i++)
		{
			pRect[i].left = pRect[(i/3)*12 + (i%3)*2].left;
			pRect[i].top = pRect[(i/3)*12 + (i%3)*2].top;
			pRect[i].right = pRect[(i/3)*12 + (i%3)*2 + 7].right;
			pRect[i].bottom = pRect[(i/3)*12 + (i%3)*2 + 7].bottom;
		}
		for (i=6; i<18; i++)
		{
			pRect[i] = pRect[i + 18];
		}
        SetRemain(0);
		break;

	case 19:
	    pRect[0].right 	= pRect[1].right;
        pRect[0].bottom = pRect[7].bottom;
        pRect[1]		= pRect[2];
        pRect[1].right 	= pRect[3].right;
        pRect[1].bottom = pRect[9].bottom;
        pRect[2]		= pRect[4];
        pRect[3]		= pRect[9];
        for (i=4; i<19; i++)
		{
			pRect[i] = pRect[i + 6];
		}
        SetRemain(0);
    	break;

	case 20:
		pRect[0].right = pRect[14].right;
		pRect[0].bottom = pRect[14].bottom;
		pRect[1].left = pRect[3].left;
		pRect[1].right = pRect[17].right;
		pRect[1].bottom = pRect[17].bottom;
		for (i=2; i<20; i++)
		{
			pRect[i] = pRect[i + 16];
		}
        SetRemain(0);
		break;

	case 21:  
		pRect[0].right = pRect[6].right;
		pRect[0].bottom = pRect[6].bottom;

		// memcpy(&pRect[1], &pRect[2], 3 * sizeof(RECT));  // hcc (重叠区域,有警告)
		
		memmove(&pRect[1], &pRect[2], 3 * sizeof(RECT));
		memcpy(&pRect[4], &pRect[7], 3 * sizeof(RECT));		// hcc (此段无重叠)

		for (i=7; i<22; i++)
		{
			pRect[i] = pRect[i + 3];
		}
        SetRemain(1);
		break;

	case 22:
	    pRect[0].right 	= pRect[1].right;
        pRect[0].bottom = pRect[7].bottom;
        for (i=1; i<=3; i++)
        {
			pRect[i] 	= pRect[i + 1];
        }
        for (i=4; i<22; i++)
		{
			pRect[i] = pRect[i + 3];
		}
        SetRemain(0);
    	break;

	case 26:
	case 27:
	    pRect[0].right 	= pRect[1].right;
        pRect[0].bottom = pRect[7].bottom;
        pRect[1]		= pRect[2];
        pRect[1].right 	= pRect[3].right;
        pRect[1].bottom = pRect[9].bottom;
        pRect[2]		= pRect[4];
        pRect[2].right 	= pRect[5].right;
        pRect[2].bottom = pRect[11].bottom;
        for (i=3; i<(piRemain == NULL ? iNums : 27); i++)
		{
			pRect[i] = pRect[i + 9];
		}
        SetRemain(27-iNums);
    	break;

    case 28:
	case 29:
	case 30:
	    pRect[0].right 	= pRect[1].right;
        pRect[0].bottom = pRect[7].bottom;
        pRect[1]		= pRect[2];
        pRect[1].right 	= pRect[3].right;
        pRect[1].bottom = pRect[9].bottom;
        pRect[2]		= pRect[4];
        pRect[3]		= pRect[5];
        for (i=4; i<(piRemain == NULL ? iNums : 30); i++)
		{
			pRect[i] = pRect[i + 6];
		}
        SetRemain(30-iNums);
    	break;

	case 31:
	case 32:
	case 33:
	    pRect[0].right 	= pRect[1].right;
        pRect[0].bottom = pRect[7].bottom;
        for (i=1; i<=4; i++)
        {
            pRect[i] 	= pRect[i + 1];
        }
        for (i=5; i<(piRemain == NULL ? iNums : 33); i++)
        {
			pRect[i] = pRect[i + 3];
		}
        SetRemain(33-iNums);
    	break;

	case 37:
    case 38:
    case 39:
    case 40:
	    pRect[0].right 	= pRect[1].right;
        pRect[0].bottom = pRect[7].bottom;
        pRect[1]		= pRect[2];
        pRect[1].right 	= pRect[3].right;
        pRect[1].bottom = pRect[9].bottom;
        pRect[2]		= pRect[4];
        pRect[2].right 	= pRect[5].right;
        pRect[2].bottom = pRect[11].bottom;
        pRect[3]		= pRect[6];
        for (i=4; i<(piRemain == NULL ? iNums : 40); i++)
        {
			pRect[i] = pRect[i + 9];
		}
    	SetRemain(40-iNums);
    	break;

	case 41:
    case 42:
    case 43:
     	pRect[0].right 	= pRect[1].right;
        pRect[0].bottom = pRect[7].bottom;
        pRect[1]		= pRect[2];
        pRect[1].right 	= pRect[3].right;
        pRect[1].bottom = pRect[9].bottom;
        pRect[2]		= pRect[4];
        pRect[3]		= pRect[5];
        pRect[4]		= pRect[6];
        for (i=5; i<(piRemain == NULL ? iNums : 43); i++)
        {
			pRect[i] = pRect[i + 6];
		}
    	SetRemain(43-iNums);
    	break;

	case 44:
    case 45:
    case 46:
    	pRect[0].right 	= pRect[1].right;
        pRect[0].bottom = pRect[7].bottom;
        for (i=1; i<=5; i++)
        {
        	pRect[i]  	= pRect[i + 1];
        }
        for (i=6; i<(piRemain == NULL ? iNums : 46); i++)
        {
			pRect[i] = pRect[i + 3];
		}
	    SetRemain(46-iNums);
    	break;

    default:
	    SetRemain(iRow*iRow - iNums);
	}
	// 将显示区域的数据拷贝回去
    if (piRemain != NULL
    	&& *piRemain > 0)
    {
        memmove(pAreaRect, pRect, (iNums + *piRemain) * sizeof(MY_RECT));
    }
    else
    {
        memmove(pAreaRect, pRect, iNums * sizeof(MY_RECT));
    }

	delete []pRect;
    
	return 1;
}
//----------------------------------------------------------------------------

//==============================================================================
// 计算区域坐标及大小 （请保证pAreaRect的数量是CalcShowNums(iNums)）
//==============================================================================
UINT8 CalcViewArea(MY_RECT *pAreaRect, MY_RECT &tWorkRect, INT32 iNums, INT32 iRowSpace)
{
    return CalcViewAreaAndRemain(pAreaRect, tWorkRect, iNums, NULL, iRowSpace);
}


/*
  Copyright (c), 2001-2022, Shenshu Tech. Co., Ltd.
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/ioctl.h>
#include <sys/poll.h>
#include <sys/time.h>
#include <fcntl.h>
#include <errno.h>
#include <pthread.h>
#include <math.h>
#include <unistd.h>
#include <signal.h>

#include "sample_comm.h"
#include "low_delay_common.h"
#include "vs_media_buffer.h"


#ifdef OT_FPGA
    #define PIC_SIZE   PIC_1080P
#else
    #define PIC_SIZE   PIC_3840X2160
#endif

#define SAMPLE_STREAM_PATH "./source_file"
#define STREAM_WIDTH 1920
#define STREAM_HEIGHT 1080
#define REF_NUM 2
#define DISPLAY_NUM 2

static ot_vo_intf_sync g_intf_sync = OT_VO_OUT_1080P60;

static vdec_display_cfg g_vdec_display_cfg = {
    .pic_size = PIC_1080P,
    .intf_sync = OT_VO_OUT_1080P60,
    .intf_type = OT_VO_INTF_HDMI,
};

static ot_size 	g_disp_size;
static td_s32   g_vdec_init = FALSE;
// vo参数 
// 解码性能(Hi3536AV100)
// −32x1080p@30fps
// −8x4K(3840*2160)@30fps

static td_u32 			g_vdec_chn_num = VDEC_VO_MAX_CHN_NUM; 		// 总窗体数 (这里不需太多 涉及VB分配)
static td_u32 			g_vpss_grp_num = VDEC_VO_MAX_CHN_NUM;
static td_bool 			g_vpss_chn_enable[OT_VPSS_MAX_CHN_NUM];
static sample_vo_cfg 	g_vo_config;
static ot_vpss_grp 		g_vpss_grp;
static sample_vdec_attr g_sample_vdec[OT_VDEC_MAX_CHN_NUM];

// 初始化时全屏创建 这么多个 vdec_chn 
static sample_vo_mode   g_sample_vo_mode =  VO_MODE_25MUX;

extern td_u32 g_bg_color;		// VO 显示器底色

// 播放时, 先指定 几分屏

static td_void sample_vdec_get_diplay_cfg()
{
#ifdef OT_FPGA
    g_vdec_display_cfg.pic_size   = PIC_720P;
    g_vdec_display_cfg.intf_sync  = OT_VO_OUT_720P60;
    g_vdec_display_cfg.intf_type   = OT_VO_INTF_BT1120;
#else
	// hcc 走这里
    if (g_intf_sync EQU OT_VO_OUT_3840x2160_30) {
        g_vdec_display_cfg.pic_size   = PIC_3840X2160;
        g_vdec_display_cfg.intf_sync  = OT_VO_OUT_3840x2160_30;
        g_vdec_display_cfg.intf_type   = OT_VO_INTF_HDMI|OT_VO_INTF_HDMI1;
    } else {
        g_vdec_display_cfg.pic_size   = PIC_1080P;
        g_vdec_display_cfg.intf_sync  = OT_VO_OUT_1080P60;
        g_vdec_display_cfg.intf_type   = OT_VO_INTF_VGA|OT_VO_INTF_HDMI|OT_VO_INTF_HDMI1;
    }
#endif
}


td_void vs_sample_comm_vdec_cal_vb_size(td_u32 chn_num, sample_vdec_attr *sample_vdec,
    td_u32 sample_vdec_arr_len, sample_vdec_buf *vdec_buf)
{
    td_u32 i;
    ot_pic_buf_attr buf_attr;

	(td_void)memset_s(&buf_attr, sizeof(ot_pic_buf_attr), 0, sizeof(ot_pic_buf_attr));
	
    for (i = 0; (i < chn_num) && (i < sample_vdec_arr_len); i++) {
        buf_attr.align = 0;
        buf_attr.height = sample_vdec[i].height;
        buf_attr.width = sample_vdec[i].width;
        if (sample_vdec[i].type == OT_PT_H265) {
            buf_attr.bit_width = sample_vdec[i].sample_vdec_video.bit_width;
            buf_attr.pixel_format = OT_PIXEL_FORMAT_YVU_SEMIPLANAR_420;
            vdec_buf[i].pic_buf_size = ot_vdec_get_pic_buf_size(sample_vdec[i].type, &buf_attr);
            vdec_buf[i].tmv_buf_size =
                ot_vdec_get_tmv_buf_size(sample_vdec[i].type, sample_vdec[i].width, sample_vdec[i].height);
        } else if (sample_vdec[i].type == OT_PT_H264) {
            buf_attr.bit_width = sample_vdec[i].sample_vdec_video.bit_width;
            buf_attr.pixel_format = OT_PIXEL_FORMAT_YVU_SEMIPLANAR_420;
            vdec_buf[i].pic_buf_size = ot_vdec_get_pic_buf_size(sample_vdec[i].type, &buf_attr);
            if (sample_vdec[i].sample_vdec_video.dec_mode == OT_VIDEO_DEC_MODE_IPB) {
                vdec_buf[i].tmv_buf_size =
                    ot_vdec_get_tmv_buf_size(sample_vdec[i].type, sample_vdec[i].width, sample_vdec[i].height);
            }
        } else {
            buf_attr.bit_width = OT_DATA_BIT_WIDTH_8;
            buf_attr.pixel_format = sample_vdec[i].sample_vdec_picture.pixel_format;
            vdec_buf[i].pic_buf_size = ot_vdec_get_pic_buf_size(sample_vdec[i].type, &buf_attr);
        }
    }
    return;
}



td_s32 vs_sample_comm_vdec_config_vb_pool(td_u32 chn_num, sample_vdec_attr *sample_vdec, td_u32 arr_len,
	sample_vdec_buf *vdec_buf, ot_vb_cfg *vb_conf)
{
	td_u32 i, j;
	td_bool find_flag;
	td_u32 pos = 0;
	/* pic_buffer */
	for (j = 0; j < OT_VB_MAX_COMMON_POOLS; j++) {
		find_flag = TD_FALSE;
		for (i = 0; (i < chn_num) && (i < arr_len); i++) {
			if ((find_flag == TD_FALSE) && (vdec_buf[i].pic_buf_size != 0) && (vdec_buf[i].pic_buf_alloc == TD_FALSE)) {
				vb_conf->common_pool[j].blk_size = vdec_buf[i].pic_buf_size;
				vb_conf->common_pool[j].blk_cnt = sample_vdec[i].frame_buf_cnt;
				vdec_buf[i].pic_buf_alloc = TD_TRUE;
				find_flag = TD_TRUE;
				pos = j;
			}

			if ((find_flag == TD_TRUE) && (vdec_buf[i].pic_buf_alloc == TD_FALSE) &&
				(vb_conf->common_pool[j].blk_size == vdec_buf[i].pic_buf_size)) {
				vb_conf->common_pool[j].blk_cnt += sample_vdec[i].frame_buf_cnt;
				vdec_buf[i].pic_buf_alloc = TD_TRUE;
			}
		}
	}

	/* tmv_buffer */
	for (j = pos + 1; j < OT_VB_MAX_COMMON_POOLS; j++) {
		find_flag = TD_FALSE;
		for (i = 0; (i < chn_num) && (i < arr_len); i++) {
			if ((find_flag == TD_FALSE) && (vdec_buf[i].tmv_buf_size != 0) && (vdec_buf[i].tmv_buf_alloc == TD_FALSE)) {
				vb_conf->common_pool[j].blk_size = vdec_buf[i].tmv_buf_size;
				vb_conf->common_pool[j].blk_cnt = sample_vdec[i].sample_vdec_video.ref_frame_num + 1;
				vdec_buf[i].tmv_buf_alloc = TD_TRUE;
				find_flag = TD_TRUE;
				pos = j;
			}

			if ((find_flag == TD_TRUE) && (vdec_buf[i].tmv_buf_alloc == TD_FALSE) &&
				(vb_conf->common_pool[j].blk_size == vdec_buf[i].tmv_buf_size)) {
				vb_conf->common_pool[j].blk_cnt += sample_vdec[i].sample_vdec_video.ref_frame_num + 1;
				vdec_buf[i].tmv_buf_alloc = TD_TRUE;
			}
		}
	}
	vb_conf->max_pool_cnt = pos + 1;
	return i - 1;
}


td_s32 vs_sample_comm_vdec_init_vb_pool(td_u32 chn_num, sample_vdec_attr *sample_vdec, td_u32 arr_len)
{
	ot_vb_cfg vb_conf;
	td_s32 ret = TD_FAILURE;
	sample_vdec_buf vdec_buf[OT_VDEC_MAX_CHN_NUM];

	check_null_ptr_return(sample_vdec);
	if (arr_len > OT_VDEC_MAX_CHN_NUM) {
		printf("sample_vdec_attr array len Invalid \n");
		return TD_FAILURE;
	}
	(td_void)memset_s(vdec_buf, sizeof(vdec_buf), 0, sizeof(sample_vdec_buf) * OT_VDEC_MAX_CHN_NUM);
	(td_void)memset_s(&vb_conf, sizeof(ot_vb_cfg), 0, sizeof(ot_vb_cfg));

	// 最大 8x4K(3840*2160)@30fps
	vs_sample_comm_vdec_cal_vb_size(chn_num, sample_vdec, arr_len, vdec_buf);
	vs_sample_comm_vdec_config_vb_pool(chn_num, sample_vdec, arr_len, vdec_buf, &vb_conf);

	// hcc 这里不做判定 使用 OT_VB_SRC_MOD OT_VDEC_DEPLOYMENT_MODE0

	// OT_VB_SRC_MOD
	{
		// 部署模式为OT_VDEC_DEPLOYMENT_MODE0的通道，模块vb使用uid为OT_VB_UID_VDEC的ModuleVB池；
		if (sample_vdec->deployment_mode == OT_VDEC_DEPLOYMENT_MODE0){
			ss_mpi_vb_exit_mod_common_pool(OT_VB_UID_VDEC);
			check_return(ss_mpi_vb_set_mod_pool_cfg(OT_VB_UID_VDEC, &vb_conf), "vb set mod pool config");
			ret = ss_mpi_vb_init_mod_common_pool(OT_VB_UID_VDEC);
			if (ret != TD_SUCCESS) {
				LogE("vb exit mod common pool fail for 0x%x\n", ret);
				ss_mpi_vb_exit_mod_common_pool(OT_VB_UID_VDEC);
				return TD_FAILURE;
			}
		}
		// 部署模式为OT_VDEC_DEPLOYMENT_MODE0的通道，模块vb使用uid为OT_VB_UID_VDEC_ADAPT的ModuleVB池
		else if (sample_vdec->deployment_mode == OT_VDEC_DEPLOYMENT_MODE1){
			ss_mpi_vb_exit_mod_common_pool(OT_VB_UID_VDEC_ADAPT);
			check_return(ss_mpi_vb_set_mod_pool_cfg(OT_VB_UID_VDEC_ADAPT, &vb_conf), "vb set mod pool config");
			ret = ss_mpi_vb_init_mod_common_pool(OT_VB_UID_VDEC_ADAPT);
			if (ret != TD_SUCCESS) {
				LogE("vb exit mod common pool fail for 0x%x\n", ret);
				ss_mpi_vb_exit_mod_common_pool(OT_VB_UID_VDEC_ADAPT);
				ss_mpi_vb_exit_mod_common_pool(OT_VB_UID_VDEC);
				return TD_FAILURE;
			}
		}
	} 

	return TD_SUCCESS;
}



static td_s32 vs_sample_init_module_vb(sample_vdec_attr *sample_vdec, td_u32 vdec_chn_num, ot_payload_type type,
    td_u32 len)
{
    td_u32 i;
    td_s32 ret;

	
    for (i = 0; (i < vdec_chn_num) && (i < len); i++) {
        sample_vdec[i].type = type;

		// hcc 先这么分配 (ion内存不够)
		// 最大支持:: 4K-->4路 500W-->8路 400W-->16路 1080P-->32路
		if (i < 8){		
         	sample_vdec[i].width   = 3840; // 4K
         	sample_vdec[i].height  = 2160;
        	sample_vdec[i].frame_buf_cnt = 1;	// 8vb
		}
		else if (i < 9){
        	sample_vdec[i].width   = 2688; // 500W
        	sample_vdec[i].height  = 1620;
        	sample_vdec[i].frame_buf_cnt = 1;	// 8vb
		}
		else if (i < 10){
        	sample_vdec[i].width   = 2560; // 400W
        	sample_vdec[i].height  = 1440;
        	sample_vdec[i].frame_buf_cnt = 1;	// 16vb
		}
		else if (i < 18){
        	sample_vdec[i].width   = 1920; // 200W
        	sample_vdec[i].height  = 1080;
        	sample_vdec[i].frame_buf_cnt = 1;	// 20vb
		}
		else {
        	sample_vdec[i].width   = 1280; // 200W
        	sample_vdec[i].height  = 720;
        	sample_vdec[i].frame_buf_cnt = 2;	// 20vb
		}
		
        sample_vdec[i].mode                        = OT_VDEC_SEND_MODE_FRAME;
        sample_vdec[i].sample_vdec_video.dec_mode  = OT_VIDEO_DEC_MODE_IP;
        sample_vdec[i].sample_vdec_video.bit_width = OT_DATA_BIT_WIDTH_8;
        if (type == OT_PT_JPEG) {
            sample_vdec[i].sample_vdec_video.ref_frame_num = 0;
        } else {
            sample_vdec[i].sample_vdec_video.ref_frame_num = REF_NUM;
        }
        sample_vdec[i].display_frame_num               = DISPLAY_NUM;		// 海思FAE display_frame_num = 1

		// 海思FAE:: 如果是mod vb的话可以按照 每个通道 * (ref_num + disp_num + 1 - 0.5) 的个数去配pic_vb，
		// 其中ref_num还早实际送的码流的参考帧去配，disp_num一般配1就够了，除非业务量大有vb轮转不过来的情况
		// hcc 删掉 ion 不够 改为1
      	sample_vdec[i].frame_buf_cnt = (type == OT_PT_JPEG) ? (sample_vdec[i].display_frame_num + 1) :
            (sample_vdec[i].sample_vdec_video.ref_frame_num + sample_vdec[i].display_frame_num + 1);
		
        if (type == OT_PT_JPEG) {
            sample_vdec[i].sample_vdec_picture.pixel_format = OT_PIXEL_FORMAT_YVU_SEMIPLANAR_420;
            sample_vdec[i].sample_vdec_picture.alpha      = 255; /* 255:pic alpha value */
        }
    }

    ret = vs_sample_comm_vdec_init_vb_pool(vdec_chn_num, &sample_vdec[0], len);
    if (ret != TD_SUCCESS) {
        LogE("init mod common vb fail for %#x!\n", ret);
        return ret;
    }

	return ret;
}


static td_s32 vs_sample_init_sys_and_vb(sample_vdec_attr *sample_vdec, td_u32 vdec_chn_num, ot_payload_type type,
    td_u32 len)
{
    ot_vb_cfg vb_cfg;
    ot_pic_buf_attr buf_attr;
    td_s32 ret;
    sample_vdec_get_diplay_cfg();
    ret = sample_comm_sys_get_pic_size(g_vdec_display_cfg.pic_size, &g_disp_size);
    if (ret != TD_SUCCESS) {
        LogE("sys get pic size fail for %#x!\n", ret);
        return ret;
    }
    
	INT32 pool_num = 0;
	INT32 blk_cnt = 4;			// 4
	
	(td_void)memset_s(&vb_cfg, sizeof(ot_vb_cfg), 0, sizeof(ot_vb_cfg));
	(td_void)memset_s(&buf_attr, sizeof(ot_pic_buf_attr), 0, sizeof(ot_pic_buf_attr));
	buf_attr.align = 0;
    buf_attr.bit_width = OT_DATA_BIT_WIDTH_8;
	buf_attr.compress_mode = OT_COMPRESS_MODE_NONE;
	buf_attr.pixel_format = OT_PIXEL_FORMAT_YVU_SEMIPLANAR_420;

	// 最大解码能力 8*4K 32*1080P

	// 这里的VB是给VO使用的 (3840*2160 1VB)	单窗口
	buf_attr.width = 3840;
	buf_attr.height = 2160;
    vb_cfg.max_pool_cnt = ++pool_num;
    vb_cfg.common_pool[pool_num-1].blk_cnt  = blk_cnt;
    vb_cfg.common_pool[pool_num-1].blk_size = ot_common_get_pic_buf_size(&buf_attr);
	LogW("4K cnt = %d, size = %d", vb_cfg.common_pool[pool_num-1].blk_cnt, vb_cfg.common_pool[pool_num-1].blk_size);
	
	// 这里的VB是给VO使用的 (2688*1620 1VB)	单窗口
	buf_attr.width = 2960;
	buf_attr.height = 1664;
    vb_cfg.max_pool_cnt = ++pool_num;
    vb_cfg.common_pool[pool_num-1].blk_cnt  = blk_cnt; /* 10:common vb cnt */
    vb_cfg.common_pool[pool_num-1].blk_size = ot_common_get_pic_buf_size(&buf_attr);
	LogW("500W cnt = %d, size = %d", vb_cfg.common_pool[pool_num-1].blk_cnt, vb_cfg.common_pool[pool_num-1].blk_size);

	// 这里的VB是给VO使用的 (2560*1440 1VB)	单窗口
	buf_attr.width = 2560;
	buf_attr.height = 1440;
    vb_cfg.max_pool_cnt = ++pool_num;
    vb_cfg.common_pool[pool_num-1].blk_cnt  = blk_cnt; /* 10:common vb cnt */
    vb_cfg.common_pool[pool_num-1].blk_size = ot_common_get_pic_buf_size(&buf_attr);
	LogW("400W cnt = %d, size = %d", vb_cfg.common_pool[pool_num-1].blk_cnt, vb_cfg.common_pool[pool_num-1].blk_size);

	// 这里的VB是给VO使用的 (1080P 1VB)	单窗口
	buf_attr.width = 1920;
    buf_attr.height = 1088;
    vb_cfg.max_pool_cnt = ++pool_num;
    vb_cfg.common_pool[pool_num-1].blk_cnt  = blk_cnt; /* 10:common vb cnt */
    vb_cfg.common_pool[pool_num-1].blk_size = ot_common_get_pic_buf_size(&buf_attr);
	LogW("1080P cnt = %d, size = %d", vb_cfg.common_pool[pool_num-1].blk_cnt, vb_cfg.common_pool[pool_num-1].blk_size);

	// 这里的VB是给VO使用的 (720P 36VB)  分屏一般使用这个VB 一般是子码流
	buf_attr.width = 1280;
    buf_attr.height = 720;
	blk_cnt = vdec_chn_num;
    vb_cfg.max_pool_cnt = ++pool_num;
    vb_cfg.common_pool[pool_num-1].blk_cnt  = blk_cnt; /* 10:common vb cnt */
    vb_cfg.common_pool[pool_num-1].blk_size = ot_common_get_pic_buf_size(&buf_attr);	
	LogW("720P cnt = %d, size = %d", vb_cfg.common_pool[pool_num-1].blk_cnt, vb_cfg.common_pool[pool_num-1].blk_size);

	// 这里的VB是给VO使用的 (720P 36VB)  分屏一般使用这个VB 一般是子码流
	buf_attr.width = 360;
    buf_attr.height = 360;
	blk_cnt = vdec_chn_num*2;
    vb_cfg.max_pool_cnt = ++pool_num;
    vb_cfg.common_pool[pool_num-1].blk_cnt  = blk_cnt; /* 10:common vb cnt */
    vb_cfg.common_pool[pool_num-1].blk_size = ot_common_get_pic_buf_size(&buf_attr);	
	LogW("640P cnt = %d, size = %d", vb_cfg.common_pool[pool_num-1].blk_cnt, vb_cfg.common_pool[pool_num-1].blk_size);

	LogW("ALL:: pool_num = %d, max_pool_cnt = %d", pool_num, vb_cfg.max_pool_cnt);
	
    ret = sample_comm_sys_init(&vb_cfg);
    if (ret != TD_SUCCESS) {
        LogE("init sys fail for %#x!\n", ret);
        sample_comm_sys_exit();
        return ret;
    }

    ret = vs_sample_init_module_vb(&sample_vdec[0], vdec_chn_num, type, len);
    if (ret != TD_SUCCESS) {
        LogE("init mod vb fail for %#x!\n", ret);
        sample_comm_vdec_exit_vb_pool();
        sample_comm_sys_exit();
        return ret;
    }
	
    return ret;
}

static td_s32 vs_sample_vdec_bind_vpss(td_u32 vpss_grp_num)
{
    td_u32 i;
    td_s32 ret;
    for (i = 0; i < vpss_grp_num; i++) {
        ret = sample_comm_vdec_bind_vpss(i, i);
        if (ret != TD_SUCCESS) {
            LogE("vdec bind vpss fail for %#x!\n", ret);
            return ret;
        }
    }
	
    return TD_SUCCESS;
}


static td_void sample_stop_vpss(ot_vpss_grp vpss_grp, td_bool *vpss_chn_enable, td_u32 chn_array_size)
{
    td_s32 i;
    for (i = vpss_grp; i >= 0; i--) {
        vpss_grp = i;
        sample_common_vpss_stop(vpss_grp, &vpss_chn_enable[0], chn_array_size);
    }
}

static td_s32 vs_sample_vdec_unbind_vpss(td_u32 vpss_grp_num)
{
    td_u32 i;
    td_s32 ret;
    for (i = 0; i < vpss_grp_num; i++) {
        ret = sample_comm_vdec_un_bind_vpss(i, i);
        if (ret != TD_SUCCESS) {
            LogE("vdec unbind vpss fail for %#x!\n", ret);
            return ret;
        }
    }
	
    return TD_SUCCESS;
}


static td_void sample_config_vpss_grp_attr(ot_vpss_grp_attr *vpss_grp_attr)
{
    vpss_grp_attr->max_width = STREAM_WIDTH;
    vpss_grp_attr->max_height = STREAM_HEIGHT;
    vpss_grp_attr->frame_rate.src_frame_rate = -1;
    vpss_grp_attr->frame_rate.dst_frame_rate = -1;
    vpss_grp_attr->pixel_format  = OT_PIXEL_FORMAT_YVU_SEMIPLANAR_420;
    vpss_grp_attr->nr_en   = TD_FALSE;
    vpss_grp_attr->ie_en   = TD_FALSE;
    vpss_grp_attr->dci_en   = TD_FALSE;
    vpss_grp_attr->dei_mode = OT_VPSS_DEI_MODE_OFF;
    vpss_grp_attr->buf_share_en   = TD_FALSE;
}



static td_s32 vs_sample_start_vpss(ot_vpss_grp *vpss_grp, td_u32 vpss_grp_num, td_bool *vpss_chn_enable, td_u32 arr_len)
{
    td_u32 i;
    td_s32 ret;
    ot_vpss_chn_attr vpss_chn_attr[OT_VPSS_MAX_CHN_NUM];
    ot_vpss_grp_attr vpss_grp_attr;

	memset(&vpss_grp_attr, 0, sizeof(ot_vpss_grp_attr));
    sample_config_vpss_grp_attr(&vpss_grp_attr);
    (td_void)memset_s(vpss_chn_enable, arr_len * sizeof(td_bool), 0, arr_len * sizeof(td_bool));
	
	if (arr_len > 1) {
		vpss_chn_enable[0] = TD_TRUE;
		vpss_chn_attr[0].width				= g_disp_size.width/3; /* 4:crop */
		vpss_chn_attr[0].height 			   	= g_disp_size.height/3; /* 4:crop */
		vpss_chn_attr[0].chn_mode			= OT_VPSS_CHN_MODE_USER;	// hcc for get_vpss_stream 
		vpss_chn_attr[0].compress_mode		= OT_COMPRESS_MODE_NONE;
		vpss_chn_attr[0].pixel_format		= OT_PIXEL_FORMAT_YVU_SEMIPLANAR_420;
		vpss_chn_attr[0].frame_rate.src_frame_rate = -1;
		vpss_chn_attr[0].frame_rate.dst_frame_rate = -1;
		vpss_chn_attr[0].depth				= 1;						// hcc 1 for get_vpss_stream 
		vpss_chn_attr[0].mirror_en			= TD_FALSE;
		vpss_chn_attr[0].flip_en			   	= TD_FALSE;
		vpss_chn_attr[0].border_en			= TD_FALSE;
		vpss_chn_attr[0].aspect_ratio.mode	= OT_ASPECT_RATIO_NONE;
	}

    for (i = 0; i < vpss_grp_num; i++) {
        *vpss_grp = i;
        ret = sample_common_vpss_start(*vpss_grp, &vpss_chn_enable[0],
            &vpss_grp_attr, vpss_chn_attr, OT_VPSS_MAX_CHN_NUM);
        if (ret != TD_SUCCESS) {
            LogE("start VPSS (%d) fail for %#x!\n", i, ret);
            sample_stop_vpss(*vpss_grp, &vpss_chn_enable[0], OT_VPSS_MAX_CHN_NUM);
            return ret;
        }
    }

#if 0
    ret = sample_start_vpss_crop(vpss_grp);
    if (ret != TD_SUCCESS) {
        sample_stop_vpss(*vpss_grp, &vpss_chn_enable[0], OT_VPSS_MAX_CHN_NUM);
        return ret;
    }
#endif

	//低延时
	//ret = sample_config_vpss_ldy_attr(vpss_grp_num);
    //if (ret != TD_SUCCESS) {
        //sample_stop_vpss(*vpss_grp, &vpss_chn_enable[0], OT_VPSS_MAX_CHN_NUM);
        //return ret;
    //}

    ret = vs_sample_vdec_bind_vpss(vpss_grp_num);
    if (ret != TD_SUCCESS) {
        vs_sample_vdec_unbind_vpss(vpss_grp_num);
        sample_stop_vpss(*vpss_grp, &vpss_chn_enable[0], OT_VPSS_MAX_CHN_NUM);
    }
	
    return ret;
}


static td_s32 sample_vpss_unbind_vo(td_u32 vpss_grp_num, sample_vo_cfg vo_config)
{
    td_u32 i;
    ot_vo_layer vo_layer = vo_config.vo_dev;
    td_s32 ret;
    for (i = 0; i < vpss_grp_num; i++) {
        ret = sample_comm_vpss_un_bind_vo(i, 0, vo_layer, i);
        if (ret != TD_SUCCESS) {
            LogE("vpss unbind vo fail for %#x!\n", ret);
            return ret;
        }
    }
	
    return TD_SUCCESS;
}



static td_s32 vs_sample_vpss_bind_vo(sample_vo_cfg vo_config, td_u32 vpss_grp_num)
{
    td_u32 i;
    ot_vo_layer vo_layer;
    td_s32 ret;
    vo_layer = vo_config.vo_dev;
    for (i = 0; i < vpss_grp_num; i++) {
        ret = sample_comm_vpss_bind_vo(i, 0, vo_layer, i);
        if (ret != TD_SUCCESS) {
            LogE("vpss bind vo fail for %#x!\n", ret);
            return ret;
        }
    }
	
    return TD_SUCCESS;
}



td_s32 vs_sample_comm_vo_start_chn(sample_vo_cfg *vo_config, ot_vo_layer vo_layer, sample_vo_mode mode)
{
	td_s32 ret;
	td_s32 i;
	sample_vo_wnd_info wnd_info;
	ot_vo_chn_attr chn_attr;
	ot_vo_video_layer_attr layer_attr;

	check_null_ptr_return(vo_config);
	memset(&wnd_info, 0, sizeof(sample_vo_wnd_info));
	
	ret = sample_comm_vo_get_wnd_info(mode, &wnd_info);
	if (ret != TD_SUCCESS) {
		LogE("failed with %#x!\n", ret);
		return ret;
	}

	LogI("wnd_info:: mode = %d, wnd_num = %d, square = %d, row = %d, col = %d", 
	  	 wnd_info.mode, wnd_info.wnd_num, wnd_info.square, wnd_info.row, wnd_info.col);

	ret = ss_mpi_vo_get_video_layer_attr(vo_layer, &layer_attr);
	if (ret != TD_SUCCESS) {
		LogE("failed with %#x!\n", ret);
		return TD_FAILURE;
	}

	for (i = 0; i < (td_s32)wnd_info.wnd_num; i++)
	{
		ret = sample_comm_vo_get_chn_attr(&wnd_info, &layer_attr, i, &chn_attr);
		if (ret != TD_SUCCESS) {
			LogE("failed with %#x!\n", ret);
			return TD_FAILURE;
		}

		ret = ss_mpi_vo_set_chn_attr(vo_layer, i, &chn_attr);
		if (ret != TD_SUCCESS) {
			LogE("failed with %#x!\n", ret);
			return TD_FAILURE;
		}

		ret = ss_mpi_vo_enable_chn(vo_layer, i);
		if (ret != TD_SUCCESS) {
			LogE("failed with %#x!\n", ret);
			return TD_FAILURE;
		}
	}

	return TD_SUCCESS;
}


td_s32 vs_sample_comm_vo_start_layer(ot_vo_layer vo_layer, const ot_vo_video_layer_attr *layer_attr)
{
    td_s32 ret;

	check_null_ptr_return(layer_attr);
	
    ret = ss_mpi_vo_set_video_layer_attr(vo_layer, layer_attr);
    if (ret != TD_SUCCESS) {
        LogE("vo_layer(%d) failed with %#x!\n", vo_layer, ret);
        return TD_FAILURE;
    }

    ret = ss_mpi_vo_enable_video_layer(vo_layer);
    if (ret != TD_SUCCESS) {
        LogE("failed with %#x!\n", ret);
        return TD_FAILURE;
    }

    return TD_SUCCESS;
}


static td_s32 vs_sample_start_vo(sample_vo_cfg *vo_config)
{
    td_s32 ret;
    vo_config->vo_dev            = SAMPLE_VO_DEV_UHD;
    vo_config->vo_layer          = 0;
    vo_config->vo_intf_type      = g_vdec_display_cfg.intf_type;
    vo_config->intf_sync         = g_vdec_display_cfg.intf_sync;
    vo_config->pic_size          = g_vdec_display_cfg.pic_size;
    vo_config->bg_color          = g_bg_color;
    vo_config->dis_buf_len       = 4; /* 3:buf length */		// 恩智 4 
    vo_config->dst_dynamic_range = OT_DYNAMIC_RANGE_SDR8;
    vo_config->vo_mode           = g_sample_vo_mode;
    vo_config->pix_format        = OT_PIXEL_FORMAT_YVU_SEMIPLANAR_420;
    vo_config->disp_rect.x       = 0;
    vo_config->disp_rect.y       = 0;
    vo_config->disp_rect.width   = g_disp_size.width;
    vo_config->disp_rect.height  = g_disp_size.height;
    vo_config->image_size.width  = g_disp_size.width;
    vo_config->image_size.height = g_disp_size.height;
    vo_config->vo_part_mode      = OT_VO_PARTITION_MODE_MULTI;
    vo_config->compress_mode     = OT_COMPRESS_MODE_NONE;
	
	LogI("disp_rect:: x = %d, y = %d, w = %d, h = %d", vo_config->disp_rect.x, vo_config->disp_rect.y, vo_config->disp_rect.width, vo_config->disp_rect.height);

    ret = sample_comm_vo_start_vo(vo_config);
    if (ret != TD_SUCCESS) {
        LogE("start VO fail for %#x!\n", ret);
        sample_comm_vo_stop_vo(vo_config);
        return ret;
    }
	
	// hcc 不绑定 解耦
//    ret = vs_sample_vpss_bind_vo(*vo_config, vpss_grp_num);
//    if (ret != TD_SUCCESS) {
//        sample_vpss_unbind_vo(vpss_grp_num, *vo_config);
//        sample_comm_vo_stop_vo(vo_config);
//    }

    return ret;
}


td_s32 sample_comm_config_ldy_attr(td_s32 i,td_bool low_delay)
{
    ot_low_delay_info ldy_attr;
    if (low_delay == TD_TRUE) {
        check_chn_return(ss_mpi_vdec_get_low_delay_attr(i, &ldy_attr), i, "ss_mpi_vdec_get_low_delay_attr");
        ldy_attr.enable = TD_TRUE;
        ldy_attr.line_cnt = 16;
        check_chn_return(ss_mpi_vdec_set_low_delay_attr(i, &ldy_attr), i, "ss_mpi_vdec_set_low_delay_attr");
    }
	
    return TD_SUCCESS;
}

static td_s32 vs_sample_start_vdec(sample_vdec_attr *sample_vdec, td_u32 vdec_chn_num, td_u32 len, td_bool is_pip)
{
    td_s32 ret;
    td_u32 i;

    ret = sample_comm_vdec_start(vdec_chn_num, &sample_vdec[0], len);
    if (ret != TD_SUCCESS) {
        LogE("start VDEC fail for %#x!\n", ret);
        sample_comm_vdec_stop(vdec_chn_num);
        return ret;
    }

    if (is_pip == TD_TRUE) {
        for (i = 0; i < vdec_chn_num; i++) {
            ret = ss_mpi_vdec_set_display_mode(i, OT_VIDEO_DISPLAY_MODE_PREVIEW);
            if (ret != TD_SUCCESS) {
                LogE("set VDEC display_mode fail for %#x!\n", ret);
                sample_comm_vdec_stop(vdec_chn_num);
            }

			ret = sample_comm_config_ldy_attr(i, TD_TRUE);
			 if (ret != TD_SUCCESS) {
                LogE("sample_comm_config_ldy_attr fail for %#x!\n", ret);
            }
        }
    }

    return ret;
}

/** 
 * [停止 vdec 和 vo 释放所有资源]
 * @param  VOID  [参数说明]
 * @return  [成功=0,失败=-1]
 */
INT32 vs_hisi_vdec_vo_uninit(VOID)
{
	td_s32 ret;
	// hcc 不绑定 解耦
	// ret = sample_vpss_unbind_vo(g_vpss_grp_num, g_vo_config);
	sample_comm_vo_stop_vo(&g_vo_config); 
	ret = vs_sample_vdec_unbind_vpss(g_vpss_grp_num);
	sample_stop_vpss(g_vpss_grp, &g_vpss_chn_enable[0], OT_VPSS_MAX_CHN_NUM);
	sample_comm_vdec_stop(g_vdec_chn_num);
	sample_comm_vdec_exit_vb_pool();	
	sample_comm_sys_exit();

	LogI("vs_sample_8wnd_stop_vdec_vpss_vo OK!");

	return ret;
}



/** 
 * [vdec 和 vo 初始化函数]
 * @param  td_void  [参数说明]
 * @return  [成功=0,失败=-1]
 */
INT32 vs_hisi_vdec_vo_init(VOID)
{
	INT32 ret = TD_FAILURE;
	
	if (g_vdec_init){
		LogE("is inited! \n");
		return TD_SUCCESS;
	}

	LogI("vs_hisi_vdec_vo_init!");

	// 校验窗口数
	if (VDEC_VO_MAX_CHN_NUM EQU 9){
		g_sample_vo_mode = VO_MODE_9MUX;
	}
	else if (VDEC_VO_MAX_CHN_NUM EQU 16){
		g_sample_vo_mode = VO_MODE_16MUX;
	}
	else if (VDEC_VO_MAX_CHN_NUM EQU 25){
		g_sample_vo_mode = VO_MODE_25MUX;
	}
	else if (VDEC_VO_MAX_CHN_NUM EQU 36){
		g_sample_vo_mode = VO_MODE_36MUX;
	}
	else if (VDEC_VO_MAX_CHN_NUM EQU 49){
		g_sample_vo_mode = VO_MODE_49MUX;
	}
	else if (VDEC_VO_MAX_CHN_NUM EQU 64){
		g_sample_vo_mode = VO_MODE_64MUX;
	}
	else {
		LogE("VDEC_VO_MAX_CHN_NUM (%d) Set ERR!! ", VDEC_VO_MAX_CHN_NUM);
		return TD_FAILURE;
	}

	/************************************************
    step2:  init VDEC
    *************************************************/
    for(INT32 i = 0;i < OT_VDEC_MAX_CHN_NUM; i++)
    {
    	g_sample_vdec[i].deployment_mode = OT_VDEC_DEPLOYMENT_MODE0;
    }

    /************************************************
    step1:  init SYS, init common VB(for VPSS and VO), init module VB(for VDEC)
    *************************************************/
    ret = vs_sample_init_sys_and_vb(&g_sample_vdec[0], g_vdec_chn_num, OT_PT_H265, g_vdec_chn_num);
    if (ret != TD_SUCCESS) {
		return ret;
    }

	ot_schedule_mode schedule_mode = OT_SCHEDULE_QUICK;
	ret = ss_mpi_sys_set_schedule_mode(&schedule_mode);
	if (ret != TD_SUCCESS) {
		LogE("ss_mpi_sys_set_schedule_mode failed with %#x!!!\n", ret);
		//return ret;
	}

	

    ret = vs_sample_start_vdec(&g_sample_vdec[0], g_vdec_chn_num, OT_VDEC_MAX_CHN_NUM, TD_TRUE);
    if (ret != TD_SUCCESS) {
        LogE("sample_start_vdec failed!!!\n");
        sample_comm_sys_exit();
    }

	/************************************************
    step3:  start VPSS
    *************************************************/
    ret = vs_sample_start_vpss(&g_vpss_grp, g_vpss_grp_num, &g_vpss_chn_enable[0], OT_VPSS_MAX_CHN_NUM);
    if (ret != TD_SUCCESS) {
        LogE("vs_sample_start_vpss failed!!!\n");
		sample_comm_vdec_stop(g_vdec_chn_num);
		sample_comm_vdec_exit_vb_pool();	
		sample_comm_sys_exit();
    }
	
	/************************************************
	step4:	start VO
	*************************************************/
	ret = vs_sample_start_vo(&g_vo_config);
	if (ret != TD_SUCCESS) {
        LogE("vs_sample_start_vo failed!!!\n");

		ret = vs_sample_vdec_unbind_vpss(g_vpss_grp_num);
		sample_stop_vpss(g_vpss_grp, &g_vpss_chn_enable[0], OT_VPSS_MAX_CHN_NUM);
		sample_comm_vdec_stop(g_vdec_chn_num);
		sample_comm_vdec_exit_vb_pool();	
		sample_comm_sys_exit();
    }

	for (INT32 i = 0; i < g_vpss_grp_num; i++){
		ret = ss_mpi_vpss_disable_user_frame_rate_ctrl(i);
		if (ret != TD_SUCCESS) {
	        LogE("ss_mpi_vpss_disable_user_frame_rate_ctrl failed with %#x!!!\n", ret);
	    }
	}

	g_vdec_init = TRUE;

	LogI("hcc VO create OK!!!");

	return ret;
}

/** 
 * [vdec 自动适配 vo 的宽高]
 * @param  vdec_chn  [vdec 通道号]
 * @param  vo_chn  [vo 通道号]
 * @return  [成功=0,失败=-1]
 */
INT32 vs_hisi_vdec_vo_adapt(INT32 vdec_chn, INT32 vo_chn)
{
	td_s32 ret = TD_FAILURE;
	
	ot_vo_layer vo_layer = 0;
	ot_vpss_grp vpss_grp = vdec_chn;
	ot_vpss_chn vpss_chn = OT_VPSS_CHN0;
	ot_vo_chn_attr vo_chn_attr;
	ot_vpss_chn_attr vpss_chn_attr;
	
	// VO宽高适配
	ret = ss_mpi_vo_get_chn_attr(vo_layer, vo_chn, &vo_chn_attr);
	if(ret != TD_SUCCESS){
		LogE("ss_mpi_vo_get_chn_attr failed with %#x!!! vo_layer=%d, vo_chn=%d\n", ret, vo_layer, vo_chn);
	}
	
	ret = ss_mpi_vpss_get_chn_attr(vpss_grp, vpss_chn, &vpss_chn_attr);
	if(ret != TD_SUCCESS){
		LogE("ss_mpi_vpss_get_chn_attr failed with %#x!!! vpss_grp=%d, vpss_chn=%d\n", ret, vpss_grp, vpss_chn);
	}

	if (vpss_chn_attr.width != vo_chn_attr.rect.width || 
		vpss_chn_attr.height != vo_chn_attr.rect.height){
		
		vpss_chn_attr.width = vo_chn_attr.rect.width;
		vpss_chn_attr.height = vo_chn_attr.rect.height;
		ret = ss_mpi_vpss_set_chn_attr(vpss_grp, vpss_chn, &vpss_chn_attr);
		if(ret != TD_SUCCESS){
			LogE("ss_mpi_vpss_set_chn_attr failed with %#x!!! vpss_grp=%d, vpss_chn=%d\n", ret, vpss_grp, vpss_chn);
		}
	}

	return ret;
}


/** 
 * [外部给 vdec 模块发流 ]
 * @param  vdec_chn  [vdec 通道号]
 * @param  vo_chn  	 [vo 通道号]
 * @param  buffer    [流数据-去掉header偏移]
 * @param  buf_len   [流长度-去掉header偏移]
 * @param  pts       [时间戳(给0)]
 * @param  is_end    [是否为结束帧]
 * @param  is_live   [实时流=TRUE 回放流=FALSE]
 * @return  [成功=0,失败=-1]
 */
INT32 vs_hisi_vdec_send_frame(INT32 vdec_chn, INT32 vo_chn, LPVOID buffer, INT32 buf_len, INT64 pts, UINT8 is_end, UINT8 is_live)
{
	UNUSED(pts);
	
	if (!g_vdec_init){
		LogE("Not init!!\n");
		return TD_FAILURE;
	}

	td_s32 ret = TD_FAILURE;
	
	ot_vo_layer vo_layer = 0;
	ot_vpss_grp vpss_grp = vdec_chn;
	ot_vpss_chn vpss_chn = OT_VPSS_CHN0;
	
    ot_vdec_stream vdec_stream;
	ot_video_frame_info vpss_stream;
	td_s32 milli_sec = 200;		// -1：阻塞 0：非阻塞 正值：超时时间
	// (td_void)memset_s(&stream, sizeof(ot_vdec_stream), 0, sizeof(ot_vdec_stream));
	//stream.pts = pts;

	vdec_stream.pts = 0;		// 海思FAE PREVIEW + pts=0
	
    vdec_stream.addr = (td_u8 *)buffer;
    vdec_stream.len = buf_len;
    vdec_stream.end_of_frame = TD_TRUE;
    vdec_stream.end_of_stream = is_end?TD_TRUE:TD_FALSE;
    vdec_stream.need_display = TD_TRUE;

	// 实时流 才需要自动适配
	if (1 || is_live){
		vs_hisi_vdec_vo_adapt(vdec_chn, vo_chn);
	}

	ret = ss_mpi_vdec_send_stream(vdec_chn, &vdec_stream, milli_sec);
	if(ret != TD_SUCCESS){
		LogE("ss_mpi_vdec_send_stream failed with %#x!!! vdec_chn=%d, vo_chn=%d\n", ret, vdec_chn, vo_chn);
	}
	
	// hcc 不使用绑定方式 直接指定播放通道
	ret = ss_mpi_vpss_get_chn_frame(vpss_grp, vpss_chn, &vpss_stream, milli_sec);
	if(ret != TD_SUCCESS){
		LogE("ss_mpi_vpss_get_chn_frame failed with %#x!!! vpss_grp=%d, vpss_chn=%d\n", ret, vpss_grp, vpss_chn);
	}

	ret = ss_mpi_vo_send_frame(vo_layer, vo_chn, &vpss_stream, milli_sec);
	if(ret != TD_SUCCESS){
		LogE("ss_mpi_vo_send_frame failed with %#x!!! vo_layer=%d, vo_chn=%d\n", ret, vo_layer, vo_chn);
	}

	ss_mpi_vpss_release_chn_frame(vpss_grp, vpss_chn, &vpss_stream);

	return ret;
}

/** 
 * [设置VO显示优先级]
 * @param  layer  	[分屏数量]
 * @param  priority [优先级 ] 
 * @return  [成功=0,失败=-1]
 */
INT32 vs_hisi_vo_set_priority(INT32 layer, INT32 priority)
{
	INT32 ret = TD_SUCCESS;
	td_u32 my_priority = 0;
	
	do {
		if (priority > OT_VO_MAX_PRIORITY){
			LogE("param ERR! max = 4, priority = %d\n", priority);
		}
		
	    ret = ss_mpi_vo_get_layer_priority(layer, &my_priority);
	    if (ret != TD_SUCCESS){
	        LogE("ss_mpi_vo_get_layer_priority failed with %#x!\n", ret);
	        break;
	    }

	    LogI("GET:: layer = %d, my_priority = %d", layer, my_priority);
	    my_priority = priority;
	    ret = ss_mpi_vo_set_layer_priority(layer, my_priority);
	    if (ret != TD_SUCCESS){
	        LogE("ss_mpi_vo_set_layer_priority failed with %#x!\n", ret);
	        break;
	    }
		
	}while(FALSE);
		
    LogI("SET:: ret = %d, layer = %d, my_priority = %d", ret, layer, my_priority);

	return ret;
}


/** 
 * [开始 指定通道 图像]
 * @param  wnd_num  [分屏个数]
 * @return  [成功=0,失败=-1]
 */
INT32 vs_hisi_vdec_start_chn(INT32 vdec_chn)
{
	if (!g_vdec_init){
		LogE("Not init!!\n");
		return TD_FAILURE;
	}
	
	INT32 s32Ret = TD_FAILURE;

	s32Ret = ss_mpi_vdec_start_recv_stream(vdec_chn);
	if (s32Ret != TD_SUCCESS){
        LogE("ss_mpi_vdec_start_recv_stream(%d) failed with %#x!\n", vdec_chn, s32Ret);
    }

	// LogI("start_vdec_chn OK! vdec_chn = %d", vdec_chn);

	return s32Ret;
}


/** 
 * [停止 指定通道 图像]
 * @param  wnd_num  [分屏个数]
 * @return  [成功=0,失败=-1]
 */
INT32 vs_hisi_vdec_stop_chn(INT32 vdec_chn)
{
	if (!g_vdec_init){
		LogE("Not init!!\n");
		return TD_FAILURE;
	}
	
	INT32 s32Ret = TD_FAILURE;

	s32Ret = ss_mpi_vdec_stop_recv_stream(vdec_chn);
	if (s32Ret != TD_SUCCESS){
        LogE("ss_mpi_vdec_stop_recv_stream(%d) failed with %#x!\n", vdec_chn, s32Ret);
    }

	// LogI("stop_vdec_chn OK! vdec_chn = %d", vdec_chn);

	return s32Ret;
}


/** 
 * [解码但不显示VO视频]
 * @param  vo_chn  [VO通道号 (和VDEC通道号一般对应)]
 * @return  [成功=0,失败=-1]
 */
INT32 vs_hisi_vo_hide_chn(INT32 vo_chn)
{
	if (!g_vdec_init){
		LogE("Not init!!\n");
		return TD_FAILURE;
	}
	
	INT32 s32Ret = TD_FAILURE;
	ot_vo_layer vo_layer = 0;

	s32Ret = ss_mpi_vo_hide_chn(vo_layer, vo_chn);
	if (s32Ret != TD_SUCCESS){
		LogE("ss_mpi_vo_hide_chn(%d) failed with %#x!\n", vo_chn, s32Ret);
	}

	// LogI("hide_vo_chn OK! vo_chn = %d", vo_chn);

	return s32Ret;
}


/** 
 * [解码且显示VO视频]
 * @param  vo_chn  [VO通道号 (和VDEC通道号一般对应)]
 * @return  [成功=0,失败=-1]
 */
INT32 vs_hisi_vo_show_chn(INT32 vo_chn)
{
	if (!g_vdec_init){
		LogE("Not init!!\n");
		return TD_FAILURE;
	}
	
	INT32 s32Ret = TD_FAILURE;
	ot_vo_layer vo_layer = 0;

	s32Ret = ss_mpi_vo_show_chn(vo_layer, vo_chn);
	if (s32Ret != TD_SUCCESS){
		LogE("ss_mpi_vo_show_chn(%d) failed with %#x!\n", vo_chn, s32Ret);
	}

	// LogI("show_vo_chn OK! vo_chn = %d", vo_chn);

	return s32Ret;
}

/** 
 * [设置窗口分屏参数]
 * @param  wnd_num  	[分屏数量]
 * @param  margin  		[预留坐标]
 * @param  is_full  	[是否全屏]
 * @param  first_chn	[第一个窗口号,主要用于全屏]
 * @return  [成功=0,失败=-1]
 * 注意: 非全屏时,margin为必填项
 */
INT32 vs_hisi_vo_set_wnd(INT32 wnd_num, ot_rect *margin, INT32 is_full, INT32 first_chn)
{
	if (!g_vdec_init){
		LogE("Not Init! \n");
		return TD_FAILURE;
	}

	INT32 s32Ret = TD_FAILURE;
	ot_vo_layer vo_layer = 0;
	ot_vo_chn_attr vo_chn_attr;
	ot_vo_video_layer_attr vo_layer_attr;
	MY_RECT	*ptRects = NULL;
	MY_RECT	workRect; 
	
	// LogI("wnd_num = %d, max_wnd = %d, is_full = %d", wnd_num, vs_hisi_vdec_get_max_chn_num(), is_full);

	if (!is_full){
		check_null_ptr_return(margin);
		if (margin->x + margin->width > g_disp_size.width || margin->y + margin->height > g_disp_size.height){
			;// return TD_FAILURE;
		}
	}
	
	if (wnd_num <= 0 || wnd_num > vs_hisi_vdec_get_max_chn_num()) {
		LogE("param err! g_vdec_chn_num = %d\n", g_vdec_chn_num);
		return s32Ret;
	}

	s32Ret = ss_mpi_vo_get_video_layer_attr(vo_layer, &vo_layer_attr);
	if (s32Ret != TD_SUCCESS){
		LogE("ss_mpi_vo_get_video_layer_attr failed with %#x!\n", s32Ret);
		return s32Ret;
	}

	if (is_full){
		workRect.left	= workRect.top = 0;
		workRect.right	= vo_layer_attr.img_size.width;
		workRect.bottom = vo_layer_attr.img_size.height;
	}
	else {
		check_null_ptr_return(margin);
		
		workRect.left	= OT_ALIGN_DOWN(margin->x, 2);
		workRect.top 	= OT_ALIGN_DOWN(margin->y, 2);
		workRect.right	= OT_ALIGN_DOWN(margin->width, 2);
		workRect.bottom = OT_ALIGN_DOWN(margin->height, 2);
	}

	// 计算分屏后的坐标布局
	ptRects = new MY_RECT[wnd_num];
	CalcViewArea(ptRects, workRect, wnd_num, 0);

    for (INT32 i = 0; i < wnd_num && i < vs_hisi_vdec_get_max_chn_num(); i++)
	{
		vo_chn_attr.rect.x		= ptRects[i].left;
		vo_chn_attr.rect.y		= ptRects[i].top;
		vo_chn_attr.rect.width	= ptRects[i].right  - ptRects[i].left;
		vo_chn_attr.rect.height	= ptRects[i].bottom - ptRects[i].top;

		// 2 
		vo_chn_attr.rect.x 		= OT_ALIGN_DOWN(vo_chn_attr.rect.x, 2);
		vo_chn_attr.rect.y 		= OT_ALIGN_DOWN(vo_chn_attr.rect.y, 2);
		vo_chn_attr.rect.width 	= OT_ALIGN_DOWN(vo_chn_attr.rect.width, 2);
		vo_chn_attr.rect.height = OT_ALIGN_DOWN(vo_chn_attr.rect.height, 2);
		
		vo_chn_attr.priority	= 0;
		vo_chn_attr.deflicker_en= TD_FALSE;

		// LogI("wnd:(vo_chn = %d) rect::(%d, %d) (%dx%d)", first_chn+i, vo_chn_attr.rect.x, vo_chn_attr.rect.y, vo_chn_attr.rect.width, vo_chn_attr.rect.height);

		s32Ret = ss_mpi_vo_set_chn_attr(vo_layer, first_chn+i, &vo_chn_attr);
		if (s32Ret != TD_SUCCESS){
			LogE("ss_mpi_vo_set_chn_attr (%d):failed with %#x!\n", first_chn+i, s32Ret);
		}
		
		#if 1
		// 海思FAE PREVIEW+pts=0
		ot_video_display_mode display_mode;
		s32Ret = ss_mpi_vdec_get_display_mode(first_chn+i, &display_mode);
		if (s32Ret != TD_SUCCESS){
			LogE("ss_mpi_vdec_get_display_mode failed with %#x!\n", s32Ret);
		}

		display_mode = OT_VIDEO_DISPLAY_MODE_PLAYBACK;
		s32Ret = ss_mpi_vdec_set_display_mode(first_chn+i, display_mode);
		if (s32Ret != TD_SUCCESS){
			LogE("ss_mpi_vdec_set_display_mode failed with %#x!\n", s32Ret);
		}
		#endif
	}

	delete []ptRects;

	return s32Ret;
}


// 抓拍通道号
#define YC_JPEG_CHN	2


// 截取实时流 jpg 
td_s32 vsipc_get_jpg_start(ot_venc_chn VencChn, ot_size* pstSize, td_bool bSupportDCF)
{
    td_s32 s32Ret;
    ot_venc_chn_attr stVencChnAttr;

    /******************************************
     step 1:  Create Venc Channel
    ******************************************/
    memset(&stVencChnAttr, 0, sizeof(stVencChnAttr));
	
    stVencChnAttr.venc_attr.type = OT_PT_JPEG;
    stVencChnAttr.venc_attr.max_pic_width     = pstSize->width;
    stVencChnAttr.venc_attr.max_pic_height    = pstSize->height;
    stVencChnAttr.venc_attr.pic_width         = pstSize->width;
    stVencChnAttr.venc_attr.pic_height        = pstSize->height;
    
	stVencChnAttr.venc_attr.buf_size = OT_ALIGN_UP(pstSize->width, 16) * OT_ALIGN_UP(pstSize->height, 16);

    LogI("set venc chn=%d, buf_size=%u, max_pic[%ux%u], pic[%ux%u]",
         VencChn, stVencChnAttr.venc_attr.buf_size,
         stVencChnAttr.venc_attr.max_pic_width,stVencChnAttr.venc_attr.max_pic_height,
         stVencChnAttr.venc_attr.pic_width, stVencChnAttr.venc_attr.pic_height);

    stVencChnAttr.venc_attr.is_by_frame       = TD_TRUE;/*get stream mode is field mode  or frame mode*/
    stVencChnAttr.venc_attr.jpeg_attr.dcf_en  = bSupportDCF;
    //stVencChnAttr.stVencAttr.stAttrJpege.bSupportXMP = TD_FALSE;
    stVencChnAttr.venc_attr.jpeg_attr.mpf_cfg.large_thumbnail_num = 0;
    stVencChnAttr.venc_attr.jpeg_attr.recv_mode = OT_VENC_PIC_RECV_SINGLE;
    stVencChnAttr.venc_attr.profile = 0;

    s32Ret = ss_mpi_venc_create_chn(VencChn, &stVencChnAttr);
    if (TD_SUCCESS != s32Ret)
    {
        LogE("ss_mpi_venc_create_chn [%d] faild with %#x!\n", VencChn, s32Ret);
        return s32Ret;
    }
	
    return s32Ret;
}


// 降低JPEG的编码质量---quality[1,99] --> [差,好]
td_s32 SetJpegParam(ot_venc_chn VeChnId, INT32 quality=50)
{
	td_s32 s32Ret = TD_FAILURE;
	ot_venc_jpeg_param stParamJpeg;
	
	s32Ret = ss_mpi_venc_get_jpeg_param(VeChnId, &stParamJpeg);
	if (TD_SUCCESS != s32Ret)
	{
		LOGE("ot_mpi_venc_get_jpeg_param failed with 0x%x\n",s32Ret);

		return TD_FAILURE;
	}

	stParamJpeg.qfactor = quality;
	s32Ret = ss_mpi_venc_set_jpeg_param(VeChnId, &stParamJpeg);
	if (TD_SUCCESS != s32Ret)
	{
		LOGE("ot_mpi_venc_set_jpeg_param failed with 0x%x\n",s32Ret);
		
		return TD_FAILURE;
	}

	return TD_SUCCESS;
}

// 写入文件
td_s32 vs_hisi_venc_save_stream(FILE *fd, ot_venc_stream *stream)
{
    td_u32 i;

    for (i = 0; i < stream->pack_cnt; i++) {
        fwrite(stream->pack[i].addr + stream->pack[i].offset, stream->pack[i].len - stream->pack[i].offset, 1, fd);

        fflush(fd);
    }

    return TD_SUCCESS;
}


INT32 vsipc_get_venc_stream(FILE *fp, ot_venc_chn jpg_venc_chn)
{
	ot_venc_chn_status 		stStat;
    ot_venc_stream 			stStream;	
	fd_set					read_fds;
	struct timeval			tv;
	td_s32 					s32VencFd;
	INT32					s32Ret = 0;
	INT32					times = 3;
	
	s32VencFd = ss_mpi_venc_get_fd(jpg_venc_chn);

	while (times--) {
		
		FD_ZERO(&read_fds);
    	FD_SET(s32VencFd, &read_fds);
		
		tv.tv_sec  = 2;
		tv.tv_usec = 0;
		s32Ret = select(s32VencFd + 1, &read_fds, NULL, NULL, &tv);
        if (s32Ret < 0)
        {
            LogE("%s() select failed!, errno=%d", __func__, errno);
			Sleep(300);
            continue;		// break 就退出了线程
        }
        else if (s32Ret EQU 0)
        {
            LogE("%s() get jpeg stream time out.", __func__);
			Sleep(300);
            continue;
        }
		
        if (FD_ISSET(s32VencFd, &read_fds)) {			
		
			s32Ret = ss_mpi_venc_query_status(jpg_venc_chn, &stStat);
		    if (s32Ret != TD_SUCCESS)
		    {
		        LogE("ot_mpi_venc_query_status failed with %#x!\n", s32Ret);
		        break;
		    }

		    /*******************************************************/
		    if (0 EQU stStat.cur_packs)
		    {
		        LogE("NOTE: Current  frame is NULL!\n");
				continue;
		    }
		    stStream.pack = (ot_venc_pack*)malloc(sizeof(ot_venc_pack) * stStat.cur_packs);
		    if (NULL EQU stStream.pack)
		    {
		        LogE("malloc memory failed!");
		        break;
		    }
		    stStream.pack_cnt = stStat.cur_packs;
		    s32Ret = ss_mpi_venc_get_stream(jpg_venc_chn, &stStream, TD_TRUE);
		    if (TD_SUCCESS != s32Ret)
		    {
		        LogE("ot_mpi_venc_get_stream failed with %#x!", s32Ret);

		        free(stStream.pack);
		        stStream.pack = NULL;
				continue;
		    }
			
			vs_hisi_venc_save_stream(fp, &stStream);

			LogW("jpeg: pack_cnt = %d, size = %d", stStream.pack_cnt, stStream.pack->len);
			
			ss_mpi_venc_release_stream(jpg_venc_chn, &stStream);
			
			free(stStream.pack);
		    stStream.pack = NULL;

			break;
        }
	}

	return s32Ret;
}


/** 
 * [从VO截取JPEG图片 并保存到filename中]
 * @param  vdec_chn [解码通道]
 * @param  vo_chn 	[VO通道]
 * @param  filename [保存文件的全路径] 
 * @return  [成功=0,失败=-1]
 */
INT32 vs_hisi_vo_cap_jpeg(INT32 vdec_chn, INT32 vo_chn, CHAR *filename)
{
	INT32 s32Ret = TD_SUCCESS;
	ot_video_frame_info frame_info;
	ot_vdec_supplement_info supplement;
	td_s32 milli_sec = 1000;
	ot_size stSize;
	ot_venc_chn jpg_venc_chn = YC_JPEG_CHN;
	ot_venc_start_param recv_param;
	
	memset(&frame_info, 0, sizeof(ot_video_frame_info));

	FILE *fp = fopen(filename, "wb");
	if (fp EQU NULL){
		LogE("filename create Err!! errno = %d", errno);
		return FAIL;
	}
	
	do{
	    s32Ret = ss_mpi_vo_get_chn_frame(0, vo_chn, &frame_info, milli_sec);
		// s32Ret = ss_mpi_vpss_get_chn_frame(vdec_chn, 0, &frame_info, milli_sec);
	    if (s32Ret != TD_SUCCESS){
	        LogE("ss_mpi_vo_get_chn_frame failed with %#x!\n", s32Ret);
	        break;
	    }

		stSize.width = frame_info.video_frame.width;
		stSize.height = frame_info.video_frame.height;

		// 设置jpg通道属性，开启通道
		s32Ret = vsipc_get_jpg_start(jpg_venc_chn, &stSize, TD_FALSE);
		if (TD_SUCCESS != s32Ret){
			LogE("vsipc_get_jpg_start failed for %#x!\n", s32Ret);
			break;
		}

		// 图片质量
		SetJpegParam(jpg_venc_chn, 90);

		recv_param.recv_pic_num = -1;		// 一直接收
		s32Ret = ss_mpi_venc_start_chn(jpg_venc_chn, &recv_param);
	    if (TD_SUCCESS != s32Ret){
	        LogE("ss_mpi_venc_start_chn faild with %#x!\n", s32Ret);
			break;
	    }

		s32Ret = ss_mpi_venc_send_frame(jpg_venc_chn, &frame_info, milli_sec);
		if (s32Ret != TD_SUCCESS){
	        LogE("ss_mpi_venc_send_frame failed with %#x!\n", s32Ret);
	        break;
	    }

		vsipc_get_venc_stream(fp, jpg_venc_chn);
		
	}while(FALSE);
	
    LogI("SET:: s32Ret = %d, file = %s, size = %d", s32Ret, filename, ftell(fp));

	ss_mpi_vo_release_chn_frame(0, vdec_chn, &frame_info);
	ss_mpi_venc_stop_chn(jpg_venc_chn);
	ss_mpi_venc_destroy_chn(jpg_venc_chn);
	CloseFileAndNull(fp);
	
	return s32Ret;
}




/**
 * 设置解码协议
 * @param  vdChn  解码通道
 * @param  is_265 h265:1；h264:0
 * @return        无
 */
VOID vs_hisi_vdec_set_protocol(INT32 vdec_chn, INT32 is_265)
{
	if (!g_vdec_init){
		LogE("Not Init! \n");
		return ;
	}
	
	INT32 				s32Ret = TD_FAILURE;
	ot_payload_type		enType = is_265 ? OT_PT_H265 : OT_PT_H264;	
	ot_vdec_chn_attr	vdec_chn_attr;
	
	if (vdec_chn >= vs_hisi_vdec_get_max_chn_num()){
		return;
	}
	
	do {
		s32Ret = ss_mpi_vdec_get_chn_attr(vdec_chn, &vdec_chn_attr);
		if(s32Ret != TD_SUCCESS)
		{
			LogE("ss_mpi_vdec_get_chn_attr(%d) fail with %#x!\n", vdec_chn, s32Ret);
			break;
		}

		if (enType == vdec_chn_attr.type){
			// LogE("same vdec_chn = %d, type = %d!\n", vdec_chn, vdec_chn_attr.type);
			break;
		}

		s32Ret = ss_mpi_vdec_stop_recv_stream(vdec_chn);
		if(s32Ret != TD_SUCCESS){
			LogE("ss_mpi_vdec_stop_recv_stream(%d) fail for %#x!\n", vdec_chn, s32Ret);
			break;
		}
		
		s32Ret = ss_mpi_vdec_destroy_chn(vdec_chn);
		if(s32Ret != TD_SUCCESS){
			LogE("ss_mpi_vdec_destroy_chn(%d) fail for %#x!\n", vdec_chn, s32Ret);
			break;
		}

		vdec_chn_attr.type = enType;		// H.264 / H.265
		s32Ret = ss_mpi_vdec_create_chn(vdec_chn, &vdec_chn_attr);
		if(s32Ret != TD_SUCCESS){
			LogE("ss_mpi_vdec_create_chn(%d) fail for %#x!\n", vdec_chn, s32Ret);
			break;
		}
		
	   	s32Ret = ss_mpi_vdec_start_recv_stream(vdec_chn);
		if(s32Ret != TD_SUCCESS){
			LogE("ss_mpi_vdec_start_recv_stream(%d) fail for %#x!\n", vdec_chn, s32Ret);
			break;
		}
	}while(TD_FALSE);
		
}



/**
 * 得到指定VO最大输出窗口
 * @return 返回最大窗口数
 */
INT32 vs_hisi_vdec_get_max_wnd(VOID)
{
	return g_vdec_chn_num;
}


/**
 * 得到最大解码通道数
 * @return 返回最大解码通道数
 */
INT32 vs_hisi_vdec_get_max_chn_num(VOID)
{
	if (!g_vdec_init){
		LogE("Not Init! \n");
		return 0;
	}

	INT32 vdec_num = 0;
	sample_vo_wnd_info wnd_info;
	memset(&wnd_info, 0, sizeof(sample_vo_wnd_info));
	
    int ret = sample_comm_vo_get_wnd_info(g_sample_vo_mode, &wnd_info);
    if (ret != TD_SUCCESS) {
        LogE("failed with %#x!\n", ret);
        return ret;
    }
	else {
		vdec_num = wnd_info.wnd_num;
	}

	return vdec_num;
}

/****************************************/
//                  jpeg 解码接口

#include "sample_vdec.h"
std::shared_ptr<JpegDecoder> JpegDecoder::m_instance = nullptr;
TCSLock JpegDecoder::m_lock;

JpegDecoder::JpegDecoder()
{
    LogI("create jpeg decoder ");
    m_init = false;
    m_max_width = 3840;
    m_max_height = 3840;

    m_vb_blk_size = (m_max_width*m_max_height*3)>>1;
    m_vb_blk_cnt = 2;

    m_vb_pool_id = OT_VB_INVALID_POOL_ID;
    m_vb_chn_pool.pic_vb_pool = OT_VB_INVALID_POOL_ID;
    m_vb_chn_pool.tmv_vb_pool = OT_VB_INVALID_POOL_ID;
    m_vdec_chn = 100;
    m_vb_src = OT_VB_SRC_MOD;
    bool ret = false;
    ot_vdec_mod_param mod_param;
    if(TD_SUCCESS ==  ss_mpi_vdec_get_mod_param(&mod_param))
        m_vb_src = mod_param.vb_src;
        
    LogI("vb src :%d",m_vb_src);
    
    do{
        if(m_vb_src EQU OT_VB_SRC_USER)
        {
            ret = vb_pool_init();
            if(!ret)
            {
                LogE("vb_pool_init error");
                break;
            }
        }
        ret = vdec_init();
        if(!ret)
        {
            LogE("vdec_init error");
            break;
        }
    }while(false);

    if(ret){
        LogI("create jpeg decoder ok this=%p",this);
        m_init = true;
    }
    else
    {
        LogE("init faild");
    }
}

JpegDecoder::~JpegDecoder()
{
    m_init = false;
    LogI("delete jpeg decoder ok this=%p",this);
    vdec_deinit();
    
    if(m_vb_src EQU OT_VB_SRC_USER)
    {
        vb_pool_deinit();
    }
}


bool JpegDecoder::vb_pool_init(void)
{
    bool ret = false;
    
    ot_vb_pool_cfg vb_pool_cfg;
    QfSet0(&vb_pool_cfg, sizeof(vb_pool_cfg));
    vb_pool_cfg.blk_cnt = m_vb_blk_cnt;
    vb_pool_cfg.blk_size = m_vb_blk_size;

    m_vb_pool_id = ss_mpi_vb_create_pool(&vb_pool_cfg);
    if (m_vb_pool_id EQU OT_VB_INVALID_POOL_ID){
        LogE("ss_mpi_vb_create_pool failed!");
        return ret;
    }
    LogI("create vb pool ok, pool id=%u",m_vb_pool_id);
    ret = true;
    return ret;
}



bool JpegDecoder::vb_pool_deinit(void)
{
    if(m_vb_pool_id != OT_VB_INVALID_POOL_ID)
    {
        td_s32 fnRet = ss_mpi_vb_destroy_pool(m_vb_pool_id);
        if(fnRet != TD_SUCCESS)
        {
            LogE("ss_mpi_vb_destroy_pool pool id =%d failed %#x ",m_vb_pool_id,fnRet);
        }
        else
        {
            LogI("ss_mpi_vb_destroy_pool pool id =%d ok",m_vb_pool_id);
        }
    }
    return true;
}

bool JpegDecoder::decode( unsigned char *jpg, unsigned int jpg_len,DataPIC_S &yuv_data)
{
    bool ret = false;
    td_s32 fnRet;
    if(!m_init)
    {
        LogE("vdec chn not init");
        return ret;
    }
    if(!jpg || jpg_len == 0)
    {
        LogE("param error jpg_len=%d",jpg_len);
        return ret;
    }
    ScopedLocker locker(m_lock);
    ot_vdec_stream stream;
    stream.end_of_frame = TD_TRUE;
    stream.end_of_stream = TD_TRUE;
    stream.need_display = TD_TRUE;
    stream.pts = 0;
    stream.private_data = 0;
    stream.addr = (td_u8 *)jpg;
    stream.len = jpg_len;
    fnRet = ss_mpi_vdec_send_stream(m_vdec_chn, &stream,1000);
    if(fnRet != TD_SUCCESS)
    {
        LogE("ss_mpi_vdec_send_stream %d  fail %#x",m_vdec_chn,fnRet);
        return ret;
    }
    ot_video_frame_info frame_info;
    fnRet = ss_mpi_vdec_get_frame(m_vdec_chn, &frame_info,NULL,1000);
    if(fnRet != TD_SUCCESS)
    {
        LogE("ss_mpi_vdec_get_frame %d  fail %#x",m_vdec_chn,fnRet);
        return ret;
    }
    
    if(!frame_info.video_frame.virt_addr[0])
    {
        INT32 y_size = frame_info.video_frame.stride[0] * frame_info.video_frame.height;
        INT32 all_yuv_size = y_size * 3 / 2; 
        
        frame_info.video_frame.virt_addr[0] = 
            ss_mpi_sys_mmap_cached(frame_info.video_frame.phys_addr[0], all_yuv_size);
        if(frame_info.video_frame.virt_addr[0] EQU NULL)
        {
            LogE("ss_mpi_sys_mmap_cached failed!");
            fnRet = ss_mpi_vdec_release_frame(m_vdec_chn,&frame_info);
            if(fnRet != TD_SUCCESS)
            {
                LogE("ss_mpi_vdec_release_frame %d  fail %#x",m_vdec_chn,fnRet);
            }
            return ret;
        }
        yuv_data.type = 2;
        yuv_data.stride = frame_info.video_frame.stride[0];
        yuv_data.width = frame_info.video_frame.width;
        yuv_data.height = frame_info.video_frame.height;
        yuv_data.len = all_yuv_size;
        char *buff = new CHAR [all_yuv_size];
        memcpy(buff,frame_info.video_frame.virt_addr[0],all_yuv_size);
        std::shared_ptr<char> sp_yuv(buff,[](char*p){delete[] p;});
        yuv_data.sp = std::move(sp_yuv);
        ret = true;
        fnRet = ss_mpi_sys_munmap(frame_info.video_frame.virt_addr[0], all_yuv_size);
        if (fnRet != TD_SUCCESS)
        {
            LogE("ss_mpi_sys_munmap failed with %#x",fnRet);
        }
    }
    fnRet = ss_mpi_vdec_release_frame(m_vdec_chn,&frame_info);
    if(fnRet != TD_SUCCESS)
    {
        LogE("ss_mpi_vdec_release_frame %d  fail %#x",m_vdec_chn,fnRet);
    }
    return ret;
}

bool JpegDecoder::vdec_init(void)
{
    ot_vdec_chn_attr vdec_chn_attr;
    
    QfSet0(&vdec_chn_attr, sizeof(vdec_chn_attr));
    
    vdec_chn_attr.type =  OT_PT_JPEG;
    vdec_chn_attr.mode = OT_VDEC_SEND_MODE_FRAME;
    vdec_chn_attr.pic_width = m_max_width;//最大宽
    vdec_chn_attr.pic_height = m_max_height;//最大高
    vdec_chn_attr.stream_buf_size = m_max_width*m_max_height*3/2;
    vdec_chn_attr.frame_buf_size = m_max_width*m_max_height*3/2;
    vdec_chn_attr.frame_buf_cnt = 2;
    vdec_chn_attr.video_attr.ref_frame_num = 0;
    vdec_chn_attr.video_attr.temporal_mvp_en = TD_FALSE;
    vdec_chn_attr.video_attr.tmv_buf_size = 0;
    ScopedLocker locker(m_lock);

    td_s32 fnRet = ss_mpi_vdec_create_chn(m_vdec_chn, &vdec_chn_attr); 
    if(fnRet != TD_SUCCESS)
    {
        LogE("ss_mpi_vdec_create_chn %d  fail %#x",m_vdec_chn,fnRet);
        return false;
    }
    
    if(m_vb_src EQU OT_VB_SRC_USER)
    {
        m_vb_chn_pool.pic_vb_pool = m_vb_pool_id;
        m_vb_chn_pool.tmv_vb_pool = OT_VB_INVALID_POOL_ID;

        fnRet = ss_mpi_vdec_attach_vb_pool (m_vdec_chn,&m_vb_chn_pool);
        if(fnRet != TD_SUCCESS)
        {
            LogE("ss_mpi_vdec_attach_vb_pool chn %d vb_pool %d  fail %#x",
                m_vdec_chn,m_vb_chn_pool.pic_vb_pool,fnRet);
            return false;
        }
    }
    ot_vdec_chn_param vdec_chn_param;
    fnRet = ss_mpi_vdec_get_chn_param(m_vdec_chn, &vdec_chn_param);
    if(fnRet != TD_SUCCESS)
    {
        LogE("ss_mpi_vdec_get_chn_param %d  fail %#x",m_vdec_chn,fnRet);
        return false;
    }
    vdec_chn_param.display_frame_num = 1;
    vdec_chn_param.pic_param.pixel_format = OT_PIXEL_FORMAT_YVU_SEMIPLANAR_420;
    vdec_chn_param.pic_param.alpha = 255;
    fnRet = ss_mpi_vdec_set_chn_param(m_vdec_chn, &vdec_chn_param);
    if(fnRet != TD_SUCCESS)
    {
        LogE("ss_mpi_vdec_set_chn_param %d  fail %#x",m_vdec_chn,fnRet);
        return false;
    }
    fnRet = ss_mpi_vdec_set_display_mode(m_vdec_chn,OT_VIDEO_DISPLAY_MODE_PREVIEW);
    if(fnRet != TD_SUCCESS){
        LogE("ss_mpi_vdec_set_display_mode %d  fail %#x",m_vdec_chn,fnRet);
        return false;
    }
    fnRet = ss_mpi_vdec_start_recv_stream(m_vdec_chn);
    if(fnRet != TD_SUCCESS){
        LogE("ss_mpi_vdec_start_recv_stream %d  fail %#x",m_vdec_chn,fnRet);
        return false;
    }
    
    LogI("vdec_init chn=%d ok",m_vdec_chn);
    return true;
}

bool JpegDecoder::vdec_deinit(void)
{
    td_s32 fnRet;
    ScopedLocker locker(m_lock);
    fnRet = ss_mpi_vdec_stop_recv_stream(m_vdec_chn);
    if(fnRet != TD_SUCCESS)
        LogE("ss_mpi_vdec_stop_recv_stream %d  fail %#x",m_vdec_chn,fnRet);
    
    if(m_vb_src EQU OT_VB_SRC_USER)
    {
        fnRet = ss_mpi_vdec_detach_vb_pool (m_vdec_chn);
        if(fnRet != TD_SUCCESS)
            LogE("ss_mpi_vdec_detach_vb_pool chn %d  %#x",m_vdec_chn,fnRet);
    }
    fnRet = ss_mpi_vdec_destroy_chn(m_vdec_chn);
    if(fnRet != TD_SUCCESS)
        LogE("ss_mpi_vdec_create_chn %d  fail %#x");
    else
        LogI("vdec_deinit chn=%d ok",m_vdec_chn);
    return true;
}


/*
  Copyright (c), 2001-2022, Shenshu Tech. Co., Ltd.
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <fcntl.h>
#include <errno.h>
#include <pthread.h>
#include <math.h>
#include <unistd.h>
#include <signal.h>
#include <limits.h>

#include <linux/fb.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/ioctl.h>
#include <sys/poll.h>
#include <sys/time.h>
#include <sys/mman.h>
#include <sys/prctl.h>
#include "securec.h"
#include "loadbmp.h"
#include "gfbg.h"
#include "sample_comm.h"
#include "low_delay_common.h"


#define FILE_LENGTH_MAX 	12
#define WIDTH_1080P 		1920
#define HEIGHT_1080P 		1080
#define WIDTH_1920      	1920
#define HEIGHT_1080     	1080

#define GRAPHICS_LAYER_G0      0
#define GRAPHICS_LAYER_G1      1
#define GRAPHICS_LAYER_G2      2
#define GRAPHICS_LAYER_G3      3
#define GRAPHICS_LAYER_G4      4

#if (defined(CONFIG_OT_GFBG_SUPPORT) && defined(CONFIG_OT_VO_SUPPORT))
#define GFBG_BE_WITH_VO    1
#else
#define GFBG_BE_WITH_VO    0
#endif

osd_color_format g_osd_color_fmt = OSD_COLOR_FORMAT_RGB1555;

td_u32 g_bg_color = COLOR_RGB_BLUE;


static struct fb_bitfield g_r16 = {10, 5, 0};
static struct fb_bitfield g_g16 = {5, 5, 0};
static struct fb_bitfield g_b16 = {0, 5, 0};
static struct fb_bitfield g_a16 = {15, 1, 0};

static struct fb_bitfield g_a32 = {24, 8, 0};
static struct fb_bitfield g_r32 = {16, 8, 0};
static struct fb_bitfield g_g32 = {8,  8, 0};
static struct fb_bitfield g_b32 = {0,  8, 0};


typedef struct {
    td_s32 fd; /* fb's file describe */
    td_s32 layer; /* which graphic layer */
    td_s32 ctrlkey; /* {0,1,2,3}={1buffer, 2buffer, 0buffer pan display, 0buffer refresh} */
    td_bool compress; /* image compressed or not */
    ot_fb_color_format color_format; /* color format. */
} pthread_gfbg_sample_info;

typedef struct {
    ot_vo_dev 		vo_dev;			// 图层ID (HDMI0=0 HDMI1=1 VGA=2)
    ot_vo_intf_type vo_intf_type;	// HDMI或BT1120
	ot_vo_intf_sync vo_intf_sync;	// 显示模式 比如1080P等等
}vo_device_info;


/** 
 * [设置VO背景色]
 * @param  color  [类似这样的颜色 0x0000FF]
 */
void vs_gfbg_set_bgcolor(int color)
{
	// COLOR_RGB_BLUE
	g_bg_color = color;
	LogW("SET:: color = %#x", color);
}

static td_s32 sample_get_file_name(pthread_gfbg_sample_info *info, td_char *file, td_u32 file_length)
{
    switch (info->layer) {
        case GRAPHICS_LAYER_G0:
            if (strncpy_s(file, file_length, "/dev/fb0", strlen("/dev/fb0")) != EOK) {
                printf("%s:%d:strncpy_s failed.\n", __FUNCTION__, __LINE__);
                return TD_FAILURE;
            }
            break;
        case GRAPHICS_LAYER_G1:
            if (strncpy_s(file, file_length, "/dev/fb1", strlen("/dev/fb1")) != EOK) {
                printf("%s:%d:strncpy_s failed.\n", __FUNCTION__, __LINE__);
                return TD_FAILURE;
            }
            break;
        case GRAPHICS_LAYER_G2:
            if (strncpy_s(file, file_length, "/dev/fb2", strlen("/dev/fb2")) != EOK) {
                printf("%s:%d:strncpy_s failed.\n", __FUNCTION__, __LINE__);
                return TD_FAILURE;
            }
            break;
        case GRAPHICS_LAYER_G3:
            if (strncpy_s(file, file_length, "/dev/fb3", strlen("/dev/fb3")) != EOK) {
                printf("%s:%d:strncpy_s failed.\n", __FUNCTION__, __LINE__);
                return TD_FAILURE;
            }
            break;
        case GRAPHICS_LAYER_G4:
            if (strncpy_s(file, file_length, "/dev/fb4", strlen("/dev/fb4")) != EOK) {
                printf("%s:%d:strncpy_s failed.\n", __FUNCTION__, __LINE__);
                return TD_FAILURE;
            }
            break;
        default:
            if (strncpy_s(file, file_length, "/dev/fb0", strlen("/dev/fb0")) != EOK) {
                printf("%s:%d:strncpy_s failed.\n", __FUNCTION__, __LINE__);
                return TD_FAILURE;
            }
            break;
    }
	
    return TD_SUCCESS;
}

static td_s32 sample_init_frame_buffer(pthread_gfbg_sample_info *info, const char *input_file)
{
    td_bool show;
    ot_fb_point point = {0, 0};
    td_char file[PATH_MAX + 1] = {0};

    if (strlen(input_file) > PATH_MAX || realpath(input_file, file) == TD_NULL) {
        return TD_FAILURE;
    }
	
    /* step 1. open framebuffer device overlay 0 */
    info->fd = open(file, O_RDWR, 0);
    if (info->fd < 0) {
        LogE("open %s failed!\n", file);
        return TD_FAILURE;
    }

    show = TD_FALSE;
    if (ioctl(info->fd, FBIOPUT_SHOW_GFBG, &show) < 0) {
        LogE("FBIOPUT_SHOW_GFBG failed!\n");
        close(info->fd);
        info->fd = -1;
        return TD_FAILURE;
    }

    /* step 2. set the screen original position */
    switch (info->ctrlkey) {
        case 3: /* 3 mouse case */
            point.x_pos = 150; /* 150 x pos */
            point.y_pos = 150; /* 150 y pos */
            break;
        default:
            point.x_pos = 0;
            point.y_pos = 0;
    }

    if (ioctl(info->fd, FBIOPUT_SCREEN_ORIGIN_GFBG, &point) < 0) {
        LogE("set screen original show position failed!\n");
        close(info->fd);
        info->fd = -1;
        return TD_FAILURE;
    }


    return TD_SUCCESS;
}

static td_s32 sample_init_var(pthread_gfbg_sample_info *info)
{
    struct fb_var_screeninfo var;

    if (ioctl(info->fd, FBIOGET_VSCREENINFO, &var) < 0) {
        LogE("get variable screen info failed!\n");
        return TD_FAILURE;
    }

    switch (info->color_format) {
        case OT_FB_FORMAT_ARGB8888:
            var.transp = g_a32;
            var.red = g_r32;
            var.green = g_g32;
            var.blue = g_b32;
            var.bits_per_pixel = 32; /* 32 for 4 byte */
            g_osd_color_fmt = OSD_COLOR_FORMAT_RGB8888;
            break;
        default:
            var.transp = g_a16;
            var.red = g_r16;
            var.green = g_g16;
            var.blue = g_b16;
            var.bits_per_pixel = 16; /* 16 for 2 byte */
            break;
    }

    switch (info->ctrlkey) {
        case 3: /* 3 mouse case */
            var.xres_virtual = 48; /* 48 for alg data */
            var.yres_virtual = 48; /* 48 for alg data */
            var.xres = 48; /* 48 for alg data */
            var.yres = 48; /* 48 for alg data */
            break;
        default:
            var.xres_virtual = WIDTH_1080P;
            var.yres_virtual = HEIGHT_1080P * 2; /* 2 for 2buf */
            var.xres = WIDTH_1080P;
            var.yres = HEIGHT_1080P;
    }
    var.activate       = FB_ACTIVATE_NOW;

    if (ioctl(info->fd, FBIOPUT_VSCREENINFO, &var) < 0) {
        LogE("put variable screen info failed!\n");
        return TD_FAILURE;
    }

    ot_fb_colorkey colorkey;
    colorkey.enable = TD_FALSE;
    colorkey.value = 0xca;
    if (ioctl(info->fd, FBIOPUT_COLORKEY_GFBG, &colorkey) < 0) {
        LogE("set screen original show position failed!\n");
        close(info->fd);
        info->fd = -1;
        return TD_FAILURE;
    }
    else {
    	LogI("hcc:: set colorkey OK!");
    }

    td_bool value = TD_FALSE;
    if (ioctl(info->fd, FBIOPUT_COMPRESSION_GFBG, &value) < 0) {
		LogE("FBIOPUT_COMPRESSION_GFBG failed!\n");
		close(info->fd);
		info->fd = -1;
		return TD_FAILURE;
    }
    else {
    	LogI("hcc:: comm set OK!");
    }

    td_bool show = TD_TRUE;
    if (ioctl(info->fd, FBIOPUT_SHOW_GFBG, &show) < 0) {
		LogE("FBIOPUT_SHOW_GFBG failed!\n");
		close(info->fd);
		info->fd = -1;
		return TD_FAILURE;
    }
    else {
    	LogI("hcc:: show OK!");
    }


    return TD_SUCCESS;
}



static td_s32 sample_gfbg_start_vo(vo_device_info *vo_dev_info)
{
    ot_vo_intf_type gfbg_vo_intf_type = vo_dev_info->vo_intf_type;
	ot_vo_intf_sync gfbg_vo_intf_sync = vo_dev_info->vo_intf_sync;

    ot_vo_dev vo_dev = vo_dev_info->vo_dev;
    ot_vo_pub_attr pub_attr;
    td_u32  vo_frm_rate;
    ot_size size;
    td_s32 ret;

	// 参考恩智VO初始化
	ot_vo_intf_type vo_intf_type_hdmi = OT_VO_INTF_HDMI;
	ot_vo_intf_type vo_intf_type_hdmi1 = OT_VO_INTF_HDMI1;
	ot_vo_intf_type vo_intf_type_vga = OT_VO_INTF_VGA;

    /* step 1(start vo):  start vo device. */
	if (gfbg_vo_intf_sync > OT_VO_OUT_1080P60){
    	pub_attr.intf_type = vo_intf_type_hdmi | vo_intf_type_hdmi1;
	}
	else {
		pub_attr.intf_type = vo_intf_type_vga | vo_intf_type_hdmi | vo_intf_type_hdmi1;
	}
	
	pub_attr.intf_sync = gfbg_vo_intf_sync;		// hcc OK 
    pub_attr.bg_color = g_bg_color;

	LogW("SET:: bg_color = %#x, gfbg_vo_intf_type = %d, gfbg_vo_intf_sync = %d", g_bg_color, gfbg_vo_intf_type, gfbg_vo_intf_sync);
	
    ret = sample_comm_vo_get_width_height(pub_attr.intf_sync, &size.width, &size.height, &vo_frm_rate);
    if (ret != TD_SUCCESS) {
        LogE("get vo width and height failed with %d!\n", ret);
        return ret;
    }
	
    ret = sample_comm_vo_start_dev(vo_dev, &pub_attr);
    if (ret != TD_SUCCESS) {
        LogE("start vo device failed with %d!\n", ret);
        return ret;
    }

    /* step 3(start vo): start hdmi device. */
    if (gfbg_vo_intf_type == OT_VO_INTF_HDMI) {
		
		// HDMI0
        ret = sample_comm_vo_hdmi_start(OT_HDMI_ID_0, pub_attr.intf_sync);
		if (ret != TD_SUCCESS) {
	        LogE("sample_comm_vo_hdmi_start(%d) failed with %d!\n", OT_HDMI_ID_0);
	    }
		else {
			LogW("sample_comm_vo_hdmi_start(%d) OKOK!\n", vo_dev);
		}

		// HDMI1
		ret = sample_comm_vo_hdmi_start(OT_HDMI_ID_1, pub_attr.intf_sync);
		if (ret != TD_SUCCESS) {
	        LogE("sample_comm_vo_hdmi_start(%d) failed with %d!\n", OT_HDMI_ID_1);
	    }
		else {
			LogW("sample_comm_vo_hdmi_start(%d) OKOK!\n", vo_dev+1);
		}

		// VGA
		ret = sample_comm_vo_bt1120_start((ot_vo_dev)2, &pub_attr);
		if (ret != TD_SUCCESS) {
	        LogE("sample_comm_vo_bt1120_start(%d) failed with %d!\n", vo_dev);
	    }
		else {
			LogW("sample_comm_vo_bt1120_start(%d) OKOK!\n", 2);
		}
    }
	
    return TD_SUCCESS;
}

static td_void sample_gfbg_stop_vo(vo_device_info *vo_dev_info)
{
#if GFBG_BE_WITH_VO
    ot_vo_intf_type gfbg_vo_intf_type = vo_dev_info->vo_intf_type;
    ot_vo_dev vo_dev = vo_dev_info->vo_dev;

    if (gfbg_vo_intf_type == OT_VO_INTF_HDMI) {
        sample_comm_vo_hdmi_stop(OT_HDMI_ID_0);
		sample_comm_vo_hdmi_stop(OT_HDMI_ID_1);
    }
    sample_comm_vo_stop_dev(vo_dev);
    return;
#else
    return;
#endif
}


INT32 vs_gfbg_start(vo_device_info *vo_dev_info)
{
	INT32 s32Ret = 0;
	pthread_gfbg_sample_info info0;
    
	td_char file[FILE_LENGTH_MAX];
	QfSet0(file, sizeof(file));
    
	ot_vo_dev vo_dev = vo_dev_info->vo_dev;
	
	// 先停止
    sample_gfbg_stop_vo(vo_dev_info);

	do {
	    s32Ret = sample_gfbg_start_vo(vo_dev_info);
	    if (s32Ret != TD_SUCCESS) {
	        LogE("sample_gfbg_start_vo(%d) start failed!\n", vo_dev_info->vo_dev);
	    }
		
	    /* step 4:  start gfbg. */
	    info0.layer = vo_dev;    	/* VO device number */
	    info0.fd = -1;
	    info0.ctrlkey = 2; 			/* 2 none buffer */
	    info0.compress = TD_FALSE; 	/* compress opened or not */
	    info0.color_format = OT_FB_FORMAT_ARGB8888;

		if (sample_get_file_name(&info0, file, FILE_LENGTH_MAX) != TD_SUCCESS) {
			LogE("sample_get_file_name failed\n");
			break;
	    }

	    if (sample_init_frame_buffer(&info0, file) != TD_SUCCESS) {
			LogE("sample_init_frame_buffer failed\n");
			break;
	    }

	    if (sample_init_var(&info0) != TD_SUCCESS) {
			LogE("sample_init_var failed\n");
	        close(info0.fd);
	        info0.fd = -1;
			break;
	    }

		s32Ret = TD_SUCCESS;
	}while(FALSE);

	return s32Ret;
}


// 启动并验证 gfbg
INT32 vs_gfbg_start_check(VOID)
{
#define		MAX_TRY_TIMES	10

	INT32 times = MAX_TRY_TIMES;
	INT32 s32Ret = 0;
	ot_vo_chn_param chn_param;
	QfSet0(&chn_param, sizeof(chn_param));
	
	vo_device_info vo_dev_info;
	QfSet0(&vo_dev_info, sizeof(vo_dev_info));

	// HDMI0 1080P 60
	vo_dev_info.vo_dev = SAMPLE_VO_DEV_DHD0;
	vo_dev_info.vo_intf_type = OT_VO_INTF_HDMI; 	/* default:HDMI or BT1120 */
	vo_dev_info.vo_intf_sync = OT_VO_OUT_1080P60; 	// OT_VO_OUT_3840x2160_30 (VGA不支持);

	vs_gfbg_start(&vo_dev_info);

	Sleep(3000);

	times = MAX_TRY_TIMES;
	while(times--){
		
		s32Ret = ss_mpi_vo_get_chn_param(vo_dev_info.vo_dev, 0, &chn_param);
		if (s32Ret != TD_SUCCESS){
			LogE("ss_mpi_vo_get_chn_param failed with %#x\n", s32Ret);
			Sleep(1000);
			continue;
		}
	}

	return s32Ret;
}



/** 
 * [图层初始化]
 * @param  VOID  [参数说明]
 * @return  [成功=0,失败=-1]
 */
INT32 vs_gfbg_init(VOID)
{
    INT32 s32Ret = TD_FAILURE;

	ot_vb_cfg vb_conf;
	QfSet0(&vb_conf, sizeof(vb_conf));

	sample_comm_sys_exit();
	sample_comm_vo_hdmi_deinit();

	do {
	    s32Ret = sample_comm_sys_init(&vb_conf);
	    if (s32Ret != TD_SUCCESS) {
	        LogE("sample_comm_sys_init failed with %d!\n", s32Ret);
			break;
	    }
		
		s32Ret = sample_comm_vo_hdmi_init();
		if (s32Ret != TD_SUCCESS) {
	        LogE("sample_comm_vo_hdmi_init failed with %d!\n", s32Ret);
			break;
	    }
		
		s32Ret = vs_gfbg_start_check();
		if (s32Ret != TD_SUCCESS) {
	        LogE("vs_gfbg_start_check failed with %d!\n", s32Ret);
			break;
	    }
		
	}while(FALSE);
	
	LogI("vs_gfbg_init ret = %d", s32Ret);
	
    return s32Ret;
}




/**
 *	网络函数
 *  
 */
#ifndef VS_NET_FUNC_H
#define VS_NET_FUNC_H

#include "vs_head.h"
#include "include/utlist.h"


#define QFPH_MULTI_ADDR		"***************"
#define TMP_NET_CFG			"/tmp/ip_config.json"           	
#define MAX_TRY_COUNT		5

#define	MAX_NET_BUF			20480

#define BUFLEN 				20480


#define ICMP_DATA_LEN 		56									//ICMP默认数据长度
#define ICMP_HEAD_LEN 		8									//ICMP默认头部长度
#define ICMP_LEN  			(ICMP_DATA_LEN + ICMP_HEAD_LEN)
#define PING_SEND_NUM 		1 									//发送报文数
#define PING_WAIT_TIME 		2									// 超时时间
#define PING_BUFFER_SIZE  	256
#define PING_ERR			(-1)
#define PING_OK				(0)

#define PRI_PING(stat, ip, nSend, nRecv)	\
				(printf("%s[%s]:: %d packets transmitted, %d received, %.2f%% packet loss\n", \
				 (stat), (ip), (nSend), (nRecv), ((nSend - nRecv) / (nSend *1.0) * 100)))


enum {
	NET_ABNORMAL_TIMEOUT_INTERFACE		= 3*60,		// 网络异常之:介质断开
	NET_ABNORMAL_TIMEOUT_INTERNET		= 10*60,	// 网络异常之:internet中断
	NET_ABNORMAL_TIMEOUT_INTERNET_NEVER = 30*60,	// 网络异常之:internet从来没连接上
};



//==============================================================================
// 国科结构体定义            >>>>>>>>
//==============================================================================

typedef struct tagSEARCH_CMD
{
	DWORD dwFlag; //0x4844
	DWORD dwCmd; //局域网搜索对应CMD_GET命令字
}SEARCH_CMD;

typedef struct tagPHONE_INFO
{
	char 	ssid[64];
    char 	psd[64];
} PHONE_INFO;

typedef struct 
{
	char 			p2pid[32];			// p2pid号
	unsigned char 	devType;			// 设备类型: 0-卡片机, 1-摇头机, 2-鱼眼摄像机, 3-nvr...
	unsigned char 	netFamily;			// 网络提供商: 1-迈特威视, 2-tutk, 3-尚云
	unsigned char 	serverID;			// 服务器ID: 保留,默认为0
	unsigned char 	language;			// 设备固件语言版本: 0为自动适应(即多语言),1为简体中文,2为繁体中文,3为英文
	unsigned int 	odmID;				// odm商ID: 0-东舜自己, 01-TCL, 02-康佳, 03-霍尼...
	unsigned int 	panoramaMode;		// 全景安装模式: 0-吸顶式,1-壁挂式
    char 			version[16];		// 版本号:  采用编译日期201612121816(年月日时分)
	char 			model[16];			// 产品规格型号
    unsigned int    web_port;           // web端口
	char			hid[20];    		// 硬件ID
	unsigned char	pf_mac[6];    		// 平台mac地址
	unsigned char	ai_lic;    			// 授权授权
	unsigned char	reserve;    		// 保留值	
	union {	
		BYTE	my_key[MY_KEY_LEN];		// 加密key	
		T_Embed_Ext_Data ext_data;		// 扩展数据 
	};
}P2P_DEVICE_INFO;
typedef struct 
{
	DWORD					dwSize;		//回复结构体的总长度
	DWORD					dwPackFlag; // == SERVER_PACK_FLAG =0x03404324
    P2P_DEVICE_INFO         p2pDeviceInfo;
}P2P_DEVICE_INFO_EX;

#define DIE_V2	sizeof(P2P_DEVICE_INFO_EX)
#define DIE_V1	(DIE_V2 - MY_KEY_LEN)

enum {
    CMD_GET           =  0x0101,    // 得到设备信息
  	CMD_SET_WIFI  	  =  0x0109,    // 设置wifi参数
	CMD_GET_DS_INFO   =  0x0113,	// 获取设备信息
	CMD_SET_DS_INFO   =  0x0114,	// 设置设备信息
    CMD_BATCH_SET_DS_INFO  =  0x0115,	// 批量设置, 除P2P_DEVICE_INFO中的p2pid其它的参数生效
    CMD_LICENSE 	  	=  0x0300,	 	// 普通授权模式
    CMD_LICENSE_FORCE 	=  0x0301,	 	// 强制重新授权
    CMD_JSON_REQUET 	=  0x0401,	 	// json请求
    CMD_JSON_RESPONE 	=  0x0402,	 	// json响应
};
#define HEAD_FLAG			0x4844
#define	SERVER_PACK_FLAG	0x03404324
#define	VS_LICENSE_FLAG		"VS_Cdyctx_License"


enum{
	NET_ST_NONE	= 0,		// 无网络
	NET_ST_ETH0,			// eth0有线
	NET_ST_ETH1,			// eth1有线
	NET_ST_DUAL_ETH,		// 双网口模式
};


/**
 * 连接internet
 * @param  state	=1则连接internet；=0则断开与internet连接, =-1则是启动的时候默认为internet未连接
 * @return         返回TRUE表示开启了时间同步, FALSE表示没有开启时间同步
 */
 enum{
	/* 连接上Internet */
	INTERNET_STATE_SUCC = 1,
	/* 与Internet断开 */
	INTERNET_STATE_ABORT = 0,
	/* 从未连接上Internet，状态未知 */
	INTERNET_STATE_UNKNOW = -1,		
};


/**
 * 应用有线网卡地址
 * @return   成功=TRUE；失败=FALSE
 */
UINT8 net_apply_net_mac();

/**
 * net初始化
 * @return   成功=OK；失败=FAIL
 */
INT32 net_init();

/**
 * net反初始化
 */
VOID net_uninit();

/**
 * 判断网络设备是否存在
 * @return   返回1表示存在；否返回0	
 */
UINT8 net_dev_exist(LPCSTR if_name);

/**
 * 判断网络设备物理连接是否可用, 但是可能由于dhcp或者ip冲突不可用
 *	有线网络网线已经插入
 *	wifi必须已经配上ssid并连接上
 * @return   返回1表示可用；否返回0	
 */
UINT8 net_dev_carrier(LPCSTR if_name);

/**
 * 判断网络是否运行并处理就绪状态
 * 	就绪时,如果ip!=NULL,将ip地址存储在里面
 * @return   返回1表示就绪；否返回0	
 */
UINT8 net_if_ready(LPCSTR if_name, LPCHAR ip);  

/**
 * 得到指定网络设备的MTU
 * @return   返回>0表示成功; 否则-1	
 */
INT32 net_get_mtu(LPCSTR if_name);  

/**
 * 设置指定网络设备的MTU
 * @return   返回>0表示成功; 否则-1	
 */
INT32 net_set_mtu(LPCSTR if_name, INT32 mtu_size);  

/**
 * 得到网关地址
 * if_name为空时,将获取默认网关地址,成功,将ip地址存储在里面
 * @return   返回1表示就绪；否返回0	
 */
UINT8 net_gw_addr(LPCSTR if_name, LPCHAR ip);


/**
 * 得到网卡mac地址
 * @return   返回1表示就绪；否返回0	
 */
UINT8 net_if_mac(LPCSTR if_name, LPCHAR mac, LPCSTR fmt=NULL);  
UINT8 net_if_mac_inc1(LPCSTR if_name, LPCHAR mac, LPCSTR fmt=NULL);
/**
 * 得到有效的mac地址,检测步骤是从NET_WIFI=>g_pEmbedInfo->ethx_mac=>NET_ETH0=>g_pEmbedInfo->eth0_mac
 *	
 * @return   返回1表示就绪；否返回0	
 */
UINT8 net_valid_if_mac(LPCSTR if_name, LPCHAR mac, LPCSTR fmt=NULL);


/** 
 * 解析MAC地址
 * @param  src_mac  源mac地址
 * @param  mac  返回的MAC地址
 * @param  fmt=NULL  格式
 * @return  返回1表示就绪；否返回0	
 */
UINT8 net_parse_mac(LPCSTR src_mac, LPCHAR mac, LPCSTR fmt=NULL);

/**
 * 开启网络接口检测
 * @return   成功=OK；失败=FAIL
 */
INT32 net_ifdetect_start();

/**
 * 停止网络接口检测
 */
VOID net_ifdetect_stop();

/**
 * 网络截入配置
 * @param  if_name 网络名,如果为NULL则自动检测
 * @return         成功=OK；失败=FAIL
 */
INT32 net_load_config(LPCSTR if_name);

/**
 * 双网口负载均衡检查
 * @return 返回当前最佳网络接口状态
 */
INT32 net_dual_eth_load_balance();


/**
 * 应用DHCP配置到网络接口
 */
UINT8 net_apply_dhcp_config(LPCSTR if_name);

/**
 * 简化的网络配置处理（基于物理连接检测）
 * @return 配置成功的接口数量
 */
INT32 net_simplified_auto_config();

/**
 * 单接口配置处理（配置文件优先 → DHCP默认）
 * @param if_name 网络接口名称
 * @return 配置成功返回TRUE，失败返回FALSE
 */
UINT8 net_configure_single_interface(LPCSTR if_name);

/**
 * 简化的启动配置线程函数
 * @param arg 线程参数（未使用）
 * @return NULL
 */
LPVOID net_simplified_startup_thread(LPVOID arg);


UINT8 net_on_internet(INT32 state);

/** 
 * 判断是否成功连接INTERNET
 *		每个平台, 不可用在平台连接线程上, 比如TUTK的登录线程或者GB28181的注册线程
 * @return  成功返回TRUE, 失败返回FALSE
 */
UINT8 net_internet_ready();

/**
 * 得到网络工作媒介
 * 返回: 0表示
 */
INT32 net_work_interface();

/**
 * 得到工作网口名称
 */
LPCSTR net_get_work_ifname();

/**
 * 域名转IP
 */
ULONG net_host_2_addr(LPCSTR host);

INT32 do_ping(CHAR *ip, INT32 sockfd);


int select_recvfrom(int fd, int sec);

// WiFi配网模式函数已移除

/** 
 * 网络设备状态
 * @param  netif  网络设备名
 * @param  t_pkts  tx字节数
 * @param  r_pkts  rx字节数
 * @param  t_drop  tx掉包数
 * @param  r_drop  rx掉包数
 * @param  carr  插入次数
 * @return  成功TRUE;失败FALSE
 */
UINT8 net_dev_status(LPCSTR netif, UINT32 *t_pkts, UINT32 *r_pkts, UINT32 *t_drop, UINT32 *r_drop, UINT32 *carr);

/**
 * 手动切换默认网关到指定的网络接口
 * @param target_if_name 目标网络接口名称（NET_ETH0 或 NET_ETH1）
 * @return TRUE=切换成功，FALSE=切换失败
 */
UINT8 net_gateway_switch_interface(LPCSTR target_if_name);

/**
 * 处理网口热插拔事件
 * @param if_name 网络接口名称
 * @param plugged TRUE=插入，FALSE=拔出
 */
VOID net_handle_hotplug_event(LPCSTR if_name, UINT8 plugged);

float net_get_pktlossrate(char* netif);

INT32 net_ip_conflict_check(LPSTR ip, UINT32 valid_num=2, UINT32 time_out=1, LPCSTR if_name=NULL);

int get_wlan0_quality(char* netif);

UINT32 vs_system_init(VOID);


#endif


/**
 *
 网络:
	1. 如果eth0可用,优先使用eth0
	2. 如果eth0断开,自动切换到eth1
	3. 支持双网口自动适应和负载均衡
	4. 保留内网搜索功能
 */
#include <sys/types.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <sys/ioctl.h>
#include <linux/if.h>
#include <netdb.h>
#include <netinet/if_ether.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <arpa/inet.h>
#include <asm/types.h>
#include <linux/netlink.h>
#include <linux/rtnetlink.h>
#include <linux/ethtool.h>
#include <linux/sockios.h>
#include <netinet/ip_icmp.h>
#include <unistd.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/reboot.h>
#include <linux/reboot.h>

#include "vs_comm_def.h"
#include "vs_net_func.h"
#include "vs_mem_buf.h"
#include "settings/vs_settings.h"
#include "json/vs_cJSON_utils.h"
#include "utils/vs_utils.h"
#include "app.h"
#include "ot_type.h"
#include "ss_mpi_sys.h"

// 工作线程
static pthread_t	g_workThread = INVALID_HANDLE_VALUE;	

// 日志重定向线程
static UINT8 		g_logs_opened = FALSE;

// 网络检测线程
static INT32		g_fd_detect = 0;
static INT32		g_if_save_state = NET_ST_NONE;
static pthread_t	g_ifd_thread = INVALID_HANDLE_VALUE;

// 网关接口切换状态管理
static CHAR		g_manual_gateway_interface[128];	// 手动设置的网关接口（空字符串=使用默认ETH0优先策略）

/**
 * 检查接口是否稳定工作
 * @param if_name 网络接口名称
 * @return TRUE=接口稳定，FALSE=需要配置
 */
static UINT8 net_is_interface_stable_and_working(LPCSTR if_name)
{
	// 简单检查：设备存在 + 物理连接 + 有IP地址
	return (if_name &&
			net_dev_exist(if_name) &&
			net_dev_carrier(if_name) &&
			net_if_ready(if_name, NULL));
}

/**
 * 严格对应关系：网络连接成功后完整保存配置
 * 确保ETH0→g_pRunSets->eth0, ETH1→g_pRunSets->eth1的严格对应
 * @param if_name 网络接口名称
 * @return 保存成功返回TRUE，失败返回FALSE
 */
static UINT8 net_auto_save_config_on_ready(LPCSTR if_name)
{
	if (!if_name) {
		LOGE("Invalid interface name for auto save");
		return FALSE;
	}

	// 检查设备是否存在
	if (!net_dev_exist(if_name)) {
		LOGE("Interface %s does not exist", if_name);
		return FALSE;
	}

	// 检查是否有IP地址
	CHAR current_ip[32];
	if (!net_if_ready(if_name, current_ip) || strlen(current_ip) == 0) {
		LOGW("Interface %s not ready or no IP for auto save", if_name);
		return FALSE;
	}

	LOGI("Auto saving network configuration for %s (IP: %s)", if_name, current_ip);

	// 获取对应的配置结构体
	UINT8 is_eth1 = (stricmp(if_name, NET_ETH1) == 0) ? TRUE : FALSE;
	T_SET_NETWORK *net = is_eth1 ? &g_pRunSets->eth1 : &g_pRunSets->eth0;

	// 同步当前网络配置信息到配置结构体

	// 1. 同步IP地址
	strncpy(net->ip, current_ip, sizeof(net->ip) - 1);
	net->ip[sizeof(net->ip) - 1] = '\0';

	// 2. 获取并同步子网掩码
	CHAR current_netmask[32] = {0};
	int skfd = socket(AF_INET, SOCK_DGRAM, 0);
	if (skfd >= 0) {
		struct ifreq ifr;
		strcpy(ifr.ifr_name, if_name);
		if (ioctl(skfd, SIOCGIFNETMASK, &ifr) >= 0) {
			struct sockaddr_in *netmask = (struct sockaddr_in *)&ifr.ifr_netmask;
			strcpy(current_netmask, inet_ntoa(netmask->sin_addr));
		} else {
			LOGW("Failed to get netmask for %s, using default", if_name);
			strcpy(current_netmask, "*************");
		}
		close(skfd);
	} else {
		LOGW("Failed to create socket for netmask, using default");
		strcpy(current_netmask, "*************");
	}
	strncpy(net->netmask, current_netmask, sizeof(net->netmask) - 1);
	net->netmask[sizeof(net->netmask) - 1] = '\0';

	// 3. 获取并同步网关地址
	CHAR current_gateway[32] = {0};
	if (net_gw_addr(if_name, current_gateway) && strlen(current_gateway) > 0) {
		strncpy(net->gateway, current_gateway, sizeof(net->gateway) - 1);
		net->gateway[sizeof(net->gateway) - 1] = '\0';
	} else {
		LOGW("Failed to get gateway for %s", if_name);
		net->gateway[0] = '\0';
	}

	// 4. 获取并同步DNS配置
	CHAR dns1[32] = {0}, dns2[32] = {0};
	FILE *resolv_file = fopen("/tmp/resolv.conf", "r");
	if (resolv_file) {
		CHAR line[128];
		int dns_count = 0;
		while (fgets(line, sizeof(line), resolv_file) && dns_count < 2) {
			if (strncmp(line, "nameserver ", 11) == 0) {
				CHAR dns_ip[32];
				if (sscanf(line + 11, "%31s", dns_ip) == 1) {
					if (dns_count == 0) {
						strcpy(dns1, dns_ip);
					} else {
						strcpy(dns2, dns_ip);
					}
					dns_count++;
				}
			}
		}
		fclose(resolv_file);
	}

	// 设置DNS到配置结构体
	if (strlen(dns1) > 0) {
		strncpy(net->dns[0], dns1, sizeof(net->dns[0]) - 1);
		net->dns[0][sizeof(net->dns[0]) - 1] = '\0';
	} else {
		strcpy(net->dns[0], "***************");  // 默认DNS
	}

	if (strlen(dns2) > 0) {
		strncpy(net->dns[1], dns2, sizeof(net->dns[1]) - 1);
		net->dns[1][sizeof(net->dns[1]) - 1] = '\0';
	} else {
		strcpy(net->dns[1], "*******");  // 默认备用DNS
	}

	// 5. 设置DHCP标志（根据当前配置方式判断）
	// 如果网关和IP在同一网段，且不是手动配置的典型地址，认为是DHCP

	// net->dhcp = TRUE;  // 默认认为是DHCP，除非明确配置为静态

	LOGI("Synced config for %s: IP=%s, Netmask=%s, Gateway=%s, DNS1=%s, DNS2=%s, DHCP=%d",
		 if_name, net->ip, net->netmask, net->gateway, net->dns[0], net->dns[1], net->dhcp);

	// 保存配置到文件
	settings_save_net(if_name);

	return TRUE;
}


/**
 * 确定网络状态
 * @param primary_if 主要网口
 * @param secondary_if 次要网口
 * @return 网络状态
 */
static INT32 net_determine_network_state(LPCSTR primary_if, LPCSTR secondary_if)
{
	UINT8 primary_ready = net_if_ready(primary_if, NULL);
	UINT8 secondary_ready = net_if_ready(secondary_if, NULL);

	if (primary_ready && secondary_ready) {
		LOGI("Dual Ethernet mode activated");
		return NET_ST_DUAL_ETH;
	} 
	else if (primary_ready) {
		LOGI("%s single mode activated", primary_if);
		return (stricmp(primary_if, NET_ETH0) == 0) ? NET_ST_ETH0 : NET_ST_ETH1;
	} 
	else {
		LOGI("No network interface ready");
		return NET_ST_NONE;
	}
}

/**
 * 网口事件处理函数
 * @param if_name 网口名称
 * @param up TRUE=连接，FALSE=断开
 * @param save_state 网络状态指针
 * @param current_time 当前时间
 * @param last_state_change_time 上次状态变更时间指针
 */
static VOID net_handle_interface_event(LPCSTR if_name, UINT8 up, INT32 *save_state, UINT32 current_time, UINT32 *last_state_change_time)
{
	if (!if_name || !save_state || !last_state_change_time) return;

	LPCSTR other_if = (stricmp(if_name, NET_ETH0) == 0) ? NET_ETH1 : NET_ETH0;

	if (!up) {
		// 断开处理
		LOGW("%s interface down", if_name);
		net_handle_hotplug_event(if_name, FALSE);

		if (net_if_ready(other_if, NULL)) {
			LOGI("Switching to %s", other_if);
			*save_state = (stricmp(other_if, NET_ETH0) == 0) ? NET_ST_ETH0 : NET_ST_ETH1;
			*last_state_change_time = current_time;
		} 
		else {
			LOGW("No available network interface");
			*save_state = NET_ST_NONE;
		}
	} 
	else {
		// 连接处理
		LOGI("%s interface up", if_name);
		net_handle_hotplug_event(if_name, TRUE);

		// 检查接口状态
		CHAR current_ip[32];
		if (net_if_ready(if_name, current_ip) && strlen(current_ip) > 0) {
			LOGI("%s already has IP: %s", if_name, current_ip);
		}

		// 确定网络状态
		*save_state = net_determine_network_state(if_name, other_if);
		*last_state_change_time = current_time;
	}
}


/**
 * 网口配置处理
 * @param if_name 网口名称
 */
static VOID net_handle_interface_configuration(LPCSTR if_name)
{
	if (!if_name) return;

	// 如果接口已稳定，跳过配置
	if (net_is_interface_stable_and_working(if_name)) {
		sync_time(TIME_CLOCK_SVR, FALSE);
		return;
	}

	// 配置接口
	if (net_configure_single_interface(if_name)) {
		LOGI("%s configuration successful", if_name);
	} 
	else {
		// 传统回退
		if (stricmp(if_name, NET_ETH0) == 0) {
			net_load_config(NET_ETH0);
			// 等待配置生效后尝试保存
			Sleep(2000);
			net_auto_save_config_on_ready(NET_ETH0);
		}
		else {
			settings_load_net(NET_ETH1);
			// 等待配置生效后尝试保存
			Sleep(2000);
			net_auto_save_config_on_ready(NET_ETH1);
		}
	}

	sync_time(TIME_CLOCK_SVR, FALSE);
}

/**
 * 网络状态监控
 * @param ip IP地址缓冲区
 * @param net_except 网络异常标志指针
 * @param lose_net_time 失网时间指针
 */
static VOID net_handle_network_state_monitoring(CHAR *ip, UINT8 *net_except, ULONG *lose_net_time)
{
	UINT8 eth0_ready = net_if_ready(NET_ETH0, NULL);
	UINT8 eth1_ready = net_if_ready(NET_ETH1, ip);

	switch (g_if_save_state) {
		case NET_ST_ETH0:
			if (!eth0_ready) {
				LOGE("ETH0 connection lost");
				*net_except = TRUE;
				if (*lose_net_time == 0) *lose_net_time = get_app_uptime();

				// 故障转移到ETH1（基于物理连接状态）
				if (eth1_ready && net_dev_carrier(NET_ETH1)) {
					LOGI("Auto failover from ETH0 to ETH1 (ETH1 has physical connection)");
					g_if_save_state = NET_ST_ETH1;
					net_load_config(NET_ETH1);
				}
				else {
					LOGW("ETH0 failed but ETH1 not available (no physical connection or not ready)");
					g_if_save_state = NET_ST_NONE;
				}
			} 
			else if (*net_except) {
				LOGI("ETH0 connection recovered");
			}
			break;

		case NET_ST_ETH1:
			if (!eth1_ready) {
				LOGE("ETH1 connection lost");
				*net_except = TRUE;
				if (*lose_net_time == 0) *lose_net_time = get_app_uptime();

				// 故障转移到ETH0（基于物理连接状态）
				if (eth0_ready && net_dev_carrier(NET_ETH0)) {
					LOGI("Auto failover from ETH1 to ETH0 (ETH0 has physical connection)");
					g_if_save_state = NET_ST_ETH0;
					net_load_config(NET_ETH0);
				}
				else {
					LOGW("ETH1 failed but ETH0 not available (no physical connection or not ready)");
					g_if_save_state = NET_ST_NONE;
				}
			} 
			else if (*net_except) {
				LOGI("ETH1 connection recovered");
			}
			break;

		case NET_ST_DUAL_ETH:
			if (!eth0_ready && !eth1_ready) {
				LOGE("Both ethernet interfaces lost");
				*net_except = TRUE;
				g_if_save_state = NET_ST_NONE;
				if (*lose_net_time == 0) *lose_net_time = get_app_uptime();
			} 
			else if (!eth0_ready && eth1_ready && net_dev_carrier(NET_ETH1)) {
				LOGW("ETH0 lost in dual mode, switching to ETH1 only (ETH1 has physical connection)");
				g_if_save_state = NET_ST_ETH1;
				net_load_config(NET_ETH1);
			}
			else if (eth0_ready && !eth1_ready && net_dev_carrier(NET_ETH0)) {
				LOGW("ETH1 lost in dual mode, switching to ETH0 only (ETH0 has physical connection)");
				g_if_save_state = NET_ST_ETH0;
				net_load_config(NET_ETH0);
			}
			else if (*net_except) {
				LOGI("Dual Ethernet connection recovered");
			}
			break;
	}

	if (g_if_save_state != NET_ST_NONE) {
		*net_except = FALSE;
		*lose_net_time = 0;
	}
}

/**
 * 单一网关逻辑：判断指定接口是否应该设置默认网关
 * 支持手动网关切换和基于物理连接状态的ETH0优先策略
 * @param if_name 网络接口名称
 * @return TRUE=应该设置网关，FALSE=不应该设置网关
 */
static UINT8 net_should_set_gateway(LPCSTR if_name)
{
	if (!if_name) {
		return FALSE;
	}

	// 检查是否有手动设置的网关接口
	if (strlen(g_manual_gateway_interface) > 0) {
		if (stricmp(if_name, g_manual_gateway_interface) == 0) {
			LOGI("Gateway: Manual gateway interface '%s' will set gateway", if_name);
			return TRUE;
		}
		else {
			LOGI("Gateway: Interface '%s' skipped (manual gateway set to '%s')", if_name, g_manual_gateway_interface);
			return FALSE;
		}
	}

	// 使用基于物理连接状态的ETH0优先策略
	if (stricmp(if_name, NET_ETH0) == 0) {
		// ETH0请求网关：只有当ETH0有物理连接时才设置网关
		if (net_dev_carrier(NET_ETH0)) {
			LOGI("Gateway: ETH0 has physical connection, ETH0 will be the gateway interface (carrier-based policy)");
			return TRUE;
		}
		else {
			LOGI("Gateway: ETH0 has no physical connection, no gateway for ETH0 (carrier-based policy)");
			return FALSE;
		}
	} else if (stricmp(if_name, NET_ETH1) == 0) {
		// ETH1请求网关：只有当ETH0没有物理连接时，ETH1才设置网关
		if (!net_dev_carrier(NET_ETH0)) {
			LOGI("Gateway: ETH0 has no physical connection, ETH1 will be the gateway interface (carrier-based policy)");
			return TRUE;
		}
		else {
			LOGI("Gateway: ETH0 has physical connection, ETH1 will not set gateway (ETH0 priority - carrier-based policy)");
			return FALSE;
		}
	}

	// 其他接口不设置网关
	return FALSE;
}


/**
 * 验证网关接口的有效性和可用性
 * @param if_name 网络接口名称
 * @return TRUE=接口有效且可用，FALSE=接口无效或不可用
 */
static UINT8 net_validate_gateway_interface(LPCSTR if_name)
{
	if (!if_name) {
		LOGE("Gateway interface validation: NULL interface name");
		return FALSE;
	}

	// 验证接口名称有效性
	if (stricmp(if_name, NET_ETH0) != 0 && stricmp(if_name, NET_ETH1) != 0) {
		LOGE("Gateway interface validation: Invalid interface name '%s' (only eth0/eth1 allowed)", if_name);
		return FALSE;
	}

	// 检查接口是否物理存在
	if (!net_dev_exist(if_name)) {
		LOGE("Gateway interface validation: Interface '%s' does not exist", if_name);
		return FALSE;
	}

	// 检查接口是否已配置IP地址
	CHAR current_ip[32];
	if (!net_if_ready(if_name, current_ip) || strlen(current_ip) == 0) {
		LOGE("Gateway interface validation: Interface '%s' not ready or no IP address", if_name);
		return FALSE;
	}

	LOGI("Gateway interface validation: Interface '%s' is valid and ready (IP: %s)", if_name, current_ip);
	return TRUE;
}


/**
 * 应用DHCP配置到网络接口
 * @param if_name 网络接口名称
 * @return 成功返回TRUE，失败返回FALSE
 */
UINT8 net_apply_dhcp_config(LPCSTR if_name)
{
	if (!if_name) {
		LOGE("Invalid interface name: NULL");
		return FALSE;
	}

	if (!net_dev_exist(if_name)) {
		LOGE("Interface %s does not exist", if_name);
		return FALSE;
	}

	LOGI("Applying DHCP configuration to %s", if_name);

	UINT8 is_eth1 = (stricmp(if_name, NET_ETH1) == 0) ? TRUE : FALSE;
	T_SET_NETWORK *net = is_eth1 ? &g_pRunSets->eth1 : &g_pRunSets->eth0;

	// 清除现有配置
	LOGI("Clearing existing configuration for %s", if_name);
	QfSet0(net->ip, sizeof(net->ip));
	QfSet0(net->netmask, sizeof(net->netmask));
	QfSet0(net->gateway, sizeof(net->gateway));
	QfSet0(net->dns, sizeof(net->dns));

	// 设置为DHCP模式
	net->dhcp = TRUE;
	LOGI("Set %s to DHCP mode", if_name);

	// 简单检查：如果接口已经有IP且DHCP客户端在运行，跳过重新配置
	CHAR current_ip[32];
	if (net_if_ready(if_name, current_ip) && strlen(current_ip) > 0) {
		CHAR check_cmd[256];
		sprintf(check_cmd, "pgrep -f \"udhcpc.*%s\" > /dev/null", if_name);
		if (system(check_cmd) == 0) {
			LOGI("Interface %s already configured with IP %s, skipping", if_name, current_ip);
			return TRUE;
		}
	}

	// 停止现有的DHCP客户端
	CHAR cmd[256];
	sprintf(cmd, "pkill -f \"udhcpc.*%s\"", if_name);
	system_run(cmd);
	Sleep(1000);

	// 清除接口IP（简单直接的方式）
	sprintf(cmd, "ifconfig %s 0.0.0.0", if_name);
	system_run(cmd);

	// 应用配置
	LOGI("Loading DHCP configuration for %s", if_name);
	INT32 result = net_load_config(if_name);
	if (result == OK) {
		// 等待DHCP获取IP地址
		LOGI("Waiting for DHCP to assign IP address to %s", if_name);
		Sleep(5000);  // 等待5秒让DHCP获取IP

		// 检查是否成功获取IP
		CHAR ip[32];
		if (net_if_ready(if_name, ip) && strlen(ip) > 0) {
			LOGI("DHCP successfully assigned IP %s to %s", ip, if_name);
			// 自动保存配置
			net_auto_save_config_on_ready(if_name);
			return TRUE;
		} 
		else {
			LOGW("DHCP did not assign IP to %s within timeout", if_name);

			return TRUE;
		}
	} 
	else {
		LOGE("Failed to load DHCP configuration for %s, result=%d", if_name, result);
		return FALSE;
	}
}

/**
 * 简化的网络配置处理（基于物理连接检测）
 * @return 配置成功的接口数量
 */
INT32 net_simplified_auto_config()
{
	LOGI("Starting simplified auto network configuration...");

	// 检测物理连接状态
	UINT8 eth0_carrier = net_dev_exist(NET_ETH0) ? net_dev_carrier(NET_ETH0) : FALSE;
	UINT8 eth1_carrier = net_dev_exist(NET_ETH1) ? net_dev_carrier(NET_ETH1) : FALSE;

	LOGI("Physical connection detection: ETH0=%d, ETH1=%d", eth0_carrier, eth1_carrier);

	INT32 configured_count = 0;
	static UINT8 eth0_config_called = FALSE;
	static UINT8 eth1_config_called = FALSE;

	// 单网卡插入场景处理
	if (eth0_carrier && !eth1_carrier) {
		LOGI("Single interface scenario: ETH0 connected");
		if (!eth0_config_called) {
			if (net_configure_single_interface(NET_ETH0)) {
				configured_count++;
				g_if_save_state = NET_ST_ETH0;
				LOGI("ETH0 configured successfully");
			}
			eth0_config_called = TRUE;
		}
	}
	else if (!eth0_carrier && eth1_carrier) {
		LOGI("Single interface scenario: ETH1 connected");
		if (!eth1_config_called) {
			if (net_configure_single_interface(NET_ETH1)) {
				configured_count++;
				g_if_save_state = NET_ST_ETH1;
				LOGI("ETH1 configured successfully");
			}
			eth1_config_called = TRUE;
		}
	}
	// 双网卡插入场景处理
	else if (eth0_carrier && eth1_carrier) {
		LOGI("Dual interface scenario: Both ETH0 and ETH1 connected");
		LOGI("Configuring both interfaces independently (dynamic gateway management will handle conflicts)");

		// 直接配置两个接口，依靠动态网关管理处理冲突
		if (!eth0_config_called) {
			if (net_configure_single_interface(NET_ETH0)) {
				configured_count++;
			}
			eth0_config_called = TRUE;
		}
		if (!eth1_config_called) {
			if (net_configure_single_interface(NET_ETH1)) {
				configured_count++;
			}
			eth1_config_called = TRUE;
		}
		if (configured_count > 0) {
			g_if_save_state = NET_ST_DUAL_ETH;
		}
	}
	else {
		LOGI("No physical connections detected");
		g_if_save_state = NET_ST_NONE;
	}

	LOGI("Simplified auto configuration completed: %d interfaces configured", configured_count);

	// 如果有接口配置成功，同步时间
	if (configured_count > 0) {
		sync_time(TIME_CLOCK_SVR, FALSE);
	}

	return configured_count;
}

/**
 * 单接口配置处理
 * @param if_name 网络接口名称
 * @return 配置成功返回TRUE，失败返回FALSE
 */
UINT8 net_configure_single_interface(LPCSTR if_name)
{
	// 统一的输入验证
	if (!if_name || !net_dev_exist(if_name) || !net_dev_carrier(if_name)) {
		LOGE("Interface %s invalid or not connected", if_name ? if_name : "NULL");
		return FALSE;
	}

	// 检查是否已配置
	CHAR current_ip[32];
	if (net_if_ready(if_name, current_ip) && strlen(current_ip) > 0) {
		LOGI("%s already configured: %s", if_name, current_ip);
		net_auto_save_config_on_ready(if_name);  // 只在成功获得IP时保存
		return TRUE;
	}

	LOGI("Configuring %s...", if_name);

	// 配置流程：配置文件 -> DHCP
	if (settings_load_net(if_name)) {
		LOGI("%s: Using config file", if_name);
		if (net_load_config(if_name) == OK) {
			Sleep(1000);  // 减少等待时间
			if (net_if_ready(if_name, current_ip)) {
				LOGI("%s: Config file success, IP: %s", if_name, current_ip);
				net_auto_save_config_on_ready(if_name);  // 只在成功获得IP时保存
				return TRUE;
			}
		}
		LOGW("%s: Config file failed, trying DHCP", if_name);
	}

	// 回退到DHCP
	LOGI("%s: Using DHCP fallback", if_name);
	if (net_apply_dhcp_config(if_name)) {
		if (net_load_config(if_name) == OK) {
			Sleep(2000);  // 减少DHCP等待时间
			if (net_if_ready(if_name, current_ip)) {
				LOGI("%s: DHCP success, IP: %s", if_name, current_ip);
				net_auto_save_config_on_ready(if_name);  // 只在成功获得IP时保存
				return TRUE;
			}
		}
	}

	LOGE("%s: Configuration failed", if_name);
	return FALSE;
}

/**
 * 简化的启动配置线程函数
 * @param arg 线程参数（未使用）
 * @return NULL
 */
LPVOID net_simplified_startup_thread(LPVOID arg)
{
	LOGI("Simplified startup auto IP configuration thread started");

	// 等待系统稳定
	Sleep(2000);

	// 执行简化的自动配置
	INT32 result = net_simplified_auto_config();

	LOGI("Simplified startup auto IP configuration thread completed, configured %d interfaces", result);

	return NULL;
}



/**
 * 处理网口热插拔事件（极简版）
 * @param if_name 网络接口名称
 * @param plugged TRUE=插入，FALSE=拔出
 */
VOID net_handle_hotplug_event(LPCSTR if_name, UINT8 plugged)
{
	if (!if_name) return;

	if (plugged) {
		// 插入：如果接口已稳定，跳过配置
		if (net_is_interface_stable_and_working(if_name)) return;
	} 
	else {
		// 拔出：清理接口
		CHAR cmd[128];
		sprintf(cmd, "ip addr flush dev %s; pkill -f \"udhcpc.*-i %s\"", if_name, if_name);
		system_run(cmd);

		// 重置手动网关
		if (strlen(g_manual_gateway_interface) > 0 && stricmp(g_manual_gateway_interface, if_name) == 0) {
			QfSet0(g_manual_gateway_interface, sizeof(g_manual_gateway_interface));
			// 保存重置状态（空字符串表示使用默认策略）
			settings_save_net_wan(g_manual_gateway_interface);
			LOGI("Gateway: Reset gateway interface setting due to '%s' unplugged", if_name);
		}
	}
}


/**
 * 双网口负载均衡检查
 * @return 返回当前最佳网络接口状态
 */
INT32 net_dual_eth_load_balance()
{
	// 检查网络接口状态
	UINT8 eth0_ready = net_if_ready(NET_ETH0, NULL);
	UINT8 eth1_ready = net_dev_exist(NET_ETH1) ? net_if_ready(NET_ETH1, NULL) : FALSE;

	// 简化的负载均衡逻辑，依靠动态网关管理处理冲突
	if (eth0_ready && eth1_ready) {
		return NET_ST_DUAL_ETH;
	}
	else if (eth0_ready) {
		return NET_ST_ETH0;
	}
	else if (eth1_ready) {
		return NET_ST_ETH1;
	}

	return NET_ST_NONE;
}


/**
 * 手动切换默认网关到指定的网络接口
 * @param target_if_name 目标网络接口名称（NET_ETH0 或 NET_ETH1）
 * @return TRUE=切换成功，FALSE=切换失败
 */
UINT8 net_gateway_switch_interface(LPCSTR target_if_name)
{
	if (!target_if_name) {
		LOGE("Gateway switch: NULL target interface name");
		return FALSE;
	}

	LOGI("Gateway switch: Attempting to switch gateway to interface '%s'", target_if_name);

	// 验证目标接口的有效性和可用性
	if (!net_validate_gateway_interface(target_if_name)) {
		LOGE("Gateway switch: Target interface '%s' validation failed", target_if_name);
		return FALSE;
	}

	// 检查是否已经是当前网关接口
	if (strlen(g_manual_gateway_interface) > 0 && stricmp(g_manual_gateway_interface, target_if_name) == 0) {
		LOGI("Gateway switch: Interface '%s' is already the current gateway interface", target_if_name);
		return TRUE;
	}

	// 记录切换前的状态
	CHAR old_interface[128];
	if (strlen(g_manual_gateway_interface) > 0) {
		strcpy(old_interface, g_manual_gateway_interface);
	} else {
		strcpy(old_interface, "default_policy");
	}

	// 设置新的手动网关接口
	if (stricmp(target_if_name, NET_ETH0) == 0) {
		strcpy(g_manual_gateway_interface, NET_ETH0);
	} else {
		strcpy(g_manual_gateway_interface, NET_ETH1);
	}

	LOGI("Gateway switch: Manual gateway interface changed from '%s' to '%s'", old_interface, g_manual_gateway_interface);

	// 重新加载目标接口的网络配置以应用新的网关设置
	LOGI("Gateway switch: Reloading network configuration for '%s'", target_if_name);
	INT32 result = net_load_config(target_if_name);
	if (result != OK) {
		LOGE("Gateway switch: Failed to reload network configuration for '%s', result=%d", target_if_name, result);
		// 回退到原来的状态
		if (stricmp(old_interface, "default_policy") != 0) {
			strcpy(g_manual_gateway_interface, old_interface);
		} else {
			QfSet0(g_manual_gateway_interface, sizeof(g_manual_gateway_interface));
		}
		// 保存回退后的状态
		settings_save_net_wan(g_manual_gateway_interface);
		LOGI("Gateway switch: Restored gateway interface setting to '%s' after failure",
			 strlen(g_manual_gateway_interface) > 0 ? g_manual_gateway_interface : "default_policy");
		return FALSE;
	}

	// 等待配置生效
	Sleep(2000);

	// 保存新的网关接口设置
	settings_save_net_wan(g_manual_gateway_interface);
	LOGI("Gateway switch: Saved gateway interface setting '%s'", g_manual_gateway_interface);

	LOGI("Gateway switch: Successfully switched gateway to interface '%s'", target_if_name);
	return TRUE;
}



LPVOID logs_thread(LPVOID arg)
{
	THREAD_FUNC_BEGIN();

	INT32	sock, new_fd;
    INT32 	fnRet, flag = 1;
	struct sockaddr_in 	svr_addr;
	struct sockaddr_in 	client_addr;
	struct timeval  	tv;
	fd_set				read_fds;
	socklen_t			skt_len;
	INT32				org_fd;

	g_logs_opened = TRUE;
	sock = socket(AF_INET, SOCK_STREAM, 0);
    if (setsockopt(sock, SOL_SOCKET, SO_REUSEADDR, &flag, sizeof(flag)) < 0) {
		LOGE("setsockopt(SO_REUSEADDR) error=%d", errno);
		goto FN_END;
	}

    QfSet0(&svr_addr, sizeof(svr_addr));
    svr_addr.sin_family = AF_INET;
    svr_addr.sin_addr.s_addr = htons(INADDR_ANY);
    svr_addr.sin_port = htons(3001);
	//set_socket_noblock(sock, O_NONBLOCK);
    if (bind(sock, (struct sockaddr *)&svr_addr, sizeof(svr_addr)) < 0) {
		LOGE("bind() error=%d", errno);
		goto FN_END;
	}

    if (listen(sock, 2) < 0) {
		LOGE("listen() error=%d", errno);
		goto FN_END;
	}

	org_fd = dup(STDOUT_FILENO);
	while (!system_quit() && g_logs_opened) {

		FD_ZERO(&read_fds);
        FD_SET(sock, &read_fds);
        tv.tv_sec  = 2;
        tv.tv_usec = 0;
		fnRet = ::select(sock+1, &read_fds, NULL, NULL, &tv);//检测socket是否有效
		if (fnRet < 0)
			break;

		if (fnRet EQU 0 ||
			!FD_ISSET(sock, &read_fds))
			continue;

		skt_len = sizeof(client_addr);
        if ((new_fd = accept(sock, (sockaddr*)&client_addr, &skt_len)) == -1) {
            LOGE_NF("Accept error:%s\n\a", strerror(errno));
            continue;
        }
		LOGI_NF("new client, %s:%d", inet_ntoa(client_addr.sin_addr), ntohs(client_addr.sin_port));

		dup2(new_fd, STDOUT_FILENO);
        close(new_fd);

	}
	dup2(org_fd, STDOUT_FILENO);

FN_END:
	close(sock);

	THREAD_FUNC_END();
}


/**
 * 内网搜索json命令
 */
VOID net_srch_json(LPCSTR recv_msg)
{
	cJSON 	*root;

	root = cJSON_Parse(recv_msg);
	if (root) {
		do {
			INT32	fnRet;
			cJSON 	*val_item = NULL;
			cJSON 	*item = cJSON_GetObjectItem(root, "cmd");

			if (strcmp(item->valuestring, "update") EQU 0) 		// 升级
			{
				val_item = cJSON_GetObjectItem(root, "model");
				if (val_item EQU NULL || strcmp(val_item->valuestring, g_pEmbedInfo->model) != 0) {
					LOGI_NF("%s() cmd:%s model mismatch", __func__, val_item->valuestring);
					break;
				}
				val_item = cJSON_GetObjectItem(root, "val");
				if (val_item)
					system_upgrade(SYS_UP_URL, val_item->valuestring);
				else
					system_upgrade(SYS_UP_AUTO, NULL);
			}
			else if (strcmp(item->valuestring, "reboot") EQU 0)	// 重启
            {
	            val_item = cJSON_GetObjectItem(root, "model");
          		if (val_item EQU NULL || strcmp(val_item->valuestring, g_pEmbedInfo->model) != 0) {
					LOGI_NF("%s() cmd:%s model mismatch", __func__, val_item->valuestring);
					break;
				}
               	fnRet = system_reboot();
            }
            else if (strcmp(item->valuestring, "reset") EQU 0)	// 恢复出厂设置
            {
            	val_item = cJSON_GetObjectItem(root, "model");
	            if (val_item EQU NULL || strcmp(val_item->valuestring, g_pEmbedInfo->model) != 0) {
					LOGI_NF("%s() cmd:%s model mismatch", __func__, val_item->valuestring);
					break;
				}
            	deltree_dir(CFG_PATH);
				Sleep(3000);
				fnRet = system_reboot();
            }
			else if (strcmp(item->valuestring, "burn-in") EQU 0)	// 老化
            {
            	val_item = cJSON_GetObjectItem(root, "model");
	            if (val_item EQU NULL || strcmp(val_item->valuestring, g_pEmbedInfo->model) != 0) {
					LOGI_NF("%s() cmd:%s model mismatch", __func__, val_item->valuestring);
					break;
				}
				if (factory_is_run()) {
					LOGE_NF("%s() factory has run", __func__);
					break;
				}
				factory_fuc(FFUNC_BURN_IN, NULL, (LPVOID)-1);

            }
			else if (strcmp(item->valuestring, "logsvr") EQU 0)	// 开启日志服务
            {
               	val_item = cJSON_GetObjectItem(root, "val");
				if (val_item)
					sys_log_upload(val_item->valuestring);
            }
			else if (strcmp(item->valuestring, "netlog") EQU 0)	// 开启网络日志
            {
            	val_item = cJSON_GetObjectItem(root, "val");
				if (val_item && val_item->valueint EQU 1) {
	            	if (!g_logs_opened) {
						g_logs_opened = TRUE;
						if (PTHREAD_CREATE(NULL, NULL, logs_thread, NULL)) {
							g_logs_opened = FALSE;
							LOGE_NF("PTHREAD_CREATE(logs_thread), errno=%d", errno);
						}
					}
				} else{
					g_logs_opened = FALSE;
				}
            }
			else if (strcmp(item->valuestring, "telnet") EQU 0)	// telnet
            {
				system_no_fd("/usr/sbin/telnetd&");
            }
			else if (strcmp(item->valuestring, "vssocket") EQU 0)	// vssocket
            {
				extern UINT8 vssocket_open();

				// vssocket_open();
            }
			else if (strcmp(item->valuestring, "web") EQU 0)	// web
            {
				system_no_fd("/opt/web/websvr -c /opt/web&");
			}
			else if (strcmp(item->valuestring, "ftp") EQU 0)	// ftp
            {
				system_no_fd("export TZ=UTC+00:00;tcpsvd 0 21 ftpd /mnt/sdcard&");
            }
			else if (strcmp(item->valuestring, "ip_config") EQU 0)	// 配置IP地址
            {
            	LOGI_NF("%s(ip_config) doc: %s", __func__, recv_msg);
            	val_item = cJSON_GetObjectItem(root, "dev");
				if (val_item && net_dev_exist(val_item->valuestring))
				{
					INT32 fd_tmp = open(TMP_NET_CFG, O_WRONLY|O_CREAT);

					if (fd_tmp) {
						UINT8	is_eth1 = stricmp(val_item->valuestring, NET_ETH1) EQU 0 ? TRUE:FALSE;
						T_SET_NETWORK 	*net = is_eth1 ? &g_pRunSets->eth1 : &g_pRunSets->eth0;

						write(fd_tmp, recv_msg, strlen(recv_msg)+1);
						close(fd_tmp);
						if (settings_load_network(TMP_NET_CFG, net)) {
							net_load_config(is_eth1 ? NET_ETH1 : NET_ETH0);

						}
					}
				}
            }
			else if (strcmp(item->valuestring, "ip_config_bc") EQU 0)	// 广播方式配置IP地址
            {
            	val_item = cJSON_GetObjectItem(root, "mac");
          		if (val_item EQU NULL || strcmp(val_item->valuestring, get_platform_mac()) != 0) {
					LOGI_NF("%s(ip_config_bc) mac:%s mismatch, dev_mac:%s", __func__, val_item->valuestring, get_platform_mac());
					break;
				}
				LOGI_NF("%s(ip_config_bc) doc: %s", __func__, recv_msg);

            	val_item = cJSON_GetObjectItem(root, "dev");
				if (val_item && net_dev_exist(val_item->valuestring))
				{
	            	INT32 fd_tmp = open(TMP_NET_CFG, O_WRONLY|O_CREAT);

					if (fd_tmp) {
						UINT8	is_eth1 = stricmp(val_item->valuestring, NET_ETH1) EQU 0 ? TRUE:FALSE;
						T_SET_NETWORK 	*net = is_eth1 ? &g_pRunSets->eth1 : &g_pRunSets->eth0;

						write(fd_tmp, recv_msg, strlen(recv_msg)+1);
						close(fd_tmp);
						if (settings_load_network(TMP_NET_CFG, net)) {
							net_load_config(is_eth1 ? NET_ETH1 : NET_ETH0);

						}
					}
				}
            }

		} while (FALSE);

	}
	else {
		LOGE("%s() err body: %s", __func__, recv_msg);
	}
	cJSON_Delete(root);
}


/**
 * 内网搜索线程
 */
LPVOID net_srch_cmd_thread(LPVOID arg)
{
	SET_THREAD_NAME();

	INT32		scktFd = (INT32)(intptr_t)arg;
	socklen_t	iSize = sizeof(sockaddr_in);
	sockaddr_in	rmtAddr;
	fd_set		read_fds;
	timeval 	tv;
	INT32		fnRet;
	BYTE		*data = (BYTE *)malloc(MAX_NET_BUF);
	PTR_MEM_BUF	writer = membuf_open(data, MAX_NET_BUF);
	PTR_MEM_BUF	reader = NULL;

	while (!system_quit()) {

		FD_ZERO(&read_fds);
		FD_SET(scktFd, &read_fds);
		tv.tv_sec 	= 2; // 2s超时
		tv.tv_usec 	= 0;
		fnRet = ::select(scktFd+1, &read_fds, NULL, NULL, &tv);//检测socket是否有效
		if (fnRet < 0)
			break;

		if (fnRet EQU 0 ||
			!FD_ISSET(scktFd, &read_fds))
			continue;

		// 接收数据
		{
			fnRet = recvfrom(scktFd, data, MAX_NET_BUF, 0, (struct sockaddr *)&rmtAddr, &iSize);
 			if (fnRet <= 0)
 				continue;

//			LOGI("recvfrom(%s:%d, len=%d)", inet_ntoa(rmtAddr.sin_addr), ntohs(rmtAddr.sin_port), fnRet);

			{
				SEARCH_CMD	*ptPackHead;

				membuf_close(&reader);
				reader = membuf_open(data, fnRet);
				membuf_rewind(writer);

				ptPackHead = (SEARCH_CMD *)membuf_read(reader, sizeof(SEARCH_CMD));
				if (ptPackHead EQU NULL ||
					ptPackHead->dwFlag != HEAD_FLAG)
					continue;

				switch (ptPackHead->dwCmd)
				{
				case CMD_GET_DS_INFO: {	// 获取设备信息
					P2P_DEVICE_INFO_EX	*ptMsgDataEx;
					CHAR	ver_buf[200];
					UINT32 	len = DIE_V2;

					ptMsgDataEx = (P2P_DEVICE_INFO_EX *)membuf_embezzle(writer, sizeof(P2P_DEVICE_INFO_EX));
					bzero(ptMsgDataEx, len);
					ptMsgDataEx->dwPackFlag = SERVER_PACK_FLAG;
					ptMsgDataEx->dwSize		= len;
					ptMsgDataEx->p2pDeviceInfo.devType		= g_pEmbedInfo->dev_type;
					ptMsgDataEx->p2pDeviceInfo.panoramaMode = g_pEmbedInfo->panorama_mode;
					ptMsgDataEx->p2pDeviceInfo.netFamily 	= DEV_FLAG_FAMILY;
					ptMsgDataEx->p2pDeviceInfo.language		= g_pEmbedInfo->language;
					ptMsgDataEx->p2pDeviceInfo.odmID		= g_pEmbedInfo->odm;
					ptMsgDataEx->p2pDeviceInfo.web_port		= VS_PORT_WEB;

#if defined(GB28181_ENABLE) && (GB_PLATFORM_ID == GB_PFID_SCDX_MJ)	// 四川电信魔镜
				#if(GB28181_VERSION==0)
					s_strcpy(ptMsgDataEx->p2pDeviceInfo.hid, 		gb_get_devid());
				#else
					s_strcpy(ptMsgDataEx->p2pDeviceInfo.hid, 		gbv2_get_devid());
				#endif
#else
					s_strcpy(ptMsgDataEx->p2pDeviceInfo.hid, 		g_pEmbedInfo->hid);
#endif
					s_strcpy(ptMsgDataEx->p2pDeviceInfo.p2pid, 		g_pEmbedInfo->p2pid);
					s_strcpy(ptMsgDataEx->p2pDeviceInfo.model, 		g_pEmbedInfo->model);
					s_strcpy(ptMsgDataEx->p2pDeviceInfo.version, 	vs_get_version(ver_buf));
					memcpy(ptMsgDataEx->p2pDeviceInfo.my_key, g_pEmbedInfo->my_key, sizeof(g_pEmbedInfo->my_key));
#if defined(VS_THRD_LICENSE)
					ptMsgDataEx->p2pDeviceInfo.ai_lic = g_pEmbedInfo->thrd_key_len ? 1 : 0;
#endif
					sscanf(get_platform_mac(),
						PF_MAC_FORMAT_HHU,
						&ptMsgDataEx->p2pDeviceInfo.pf_mac[0],
						&ptMsgDataEx->p2pDeviceInfo.pf_mac[1],
						&ptMsgDataEx->p2pDeviceInfo.pf_mac[2],
						&ptMsgDataEx->p2pDeviceInfo.pf_mac[3],
						&ptMsgDataEx->p2pDeviceInfo.pf_mac[4],
						&ptMsgDataEx->p2pDeviceInfo.pf_mac[5]);
//					LOGI_NF("platform_mac(%s), parse: " PF_MAC_FORMAT_HHU, get_platform_mac(),
//						ptMsgDataEx->p2pDeviceInfo.pf_mac[0],
//						ptMsgDataEx->p2pDeviceInfo.pf_mac[1],
//						ptMsgDataEx->p2pDeviceInfo.pf_mac[2],
//						ptMsgDataEx->p2pDeviceInfo.pf_mac[3],
//						ptMsgDataEx->p2pDeviceInfo.pf_mac[4],
//						ptMsgDataEx->p2pDeviceInfo.pf_mac[5]);
					{
						cJSON	*root = cJSON_CreateObject();

						{
							char chipsn[64];

							// 芯片序列号
							QfSet0(chipsn, sizeof(chipsn));
							vsipc_get_ml_sn(chipsn, sizeof(chipsn)-1);
							cJSON_AddStringToObject(root, "chipsn", chipsn);
						}
						cJSON_AddStringToObject(root, "hid", g_pEmbedInfo->hid);
						LPCSTR lte_info = file_exist(JS_LTE_STATE) ? JS_LTE_STATE : JS_LTE_INFO;
						jparse_value_fun(lte_info, "$.body", [&](cJSON *item){
							if (cJSON_IsObject(item)){
								cJSON *sub_item = cJSON_GetObjectItem(item, "imei");

								if (sub_item && cJSON_IsString(sub_item))
									cJSON_AddStringToObject(root, "imei", sub_item->valuestring);

								sub_item = cJSON_GetObjectItem(item, "ccid");
								if (sub_item && cJSON_IsString(sub_item))
									cJSON_AddStringToObject(root, "ccid", sub_item->valuestring);
							}
						});

						{
							//增加网络配置信息
							settings_load_network(CFG_ETH("/tmp/"), &g_pRunSets->eth0);


							cJSON *net_info = cJSON_CreateObject();
							cJSON *net_cfg = cJSON_Load(CFG_ETH(CFG_PATH));
							if(net_cfg != NULL)
							{
								cJSON *item=NULL;
								item = cJSON_GetObjectItem(net_cfg, "ip");
								if(item != NULL)
									cJSON_AddStringToObject(net_info, "ip",item->valuestring);

								item = cJSON_GetObjectItem(net_cfg, "dhcp");
								if(item != NULL)
									cJSON_AddNumberToObject(net_info, "dhcp",item->valueint);

								item = cJSON_GetObjectItem(net_cfg, "netmask");
								if(item != NULL)
									cJSON_AddStringToObject(net_info, "netmask",item->valuestring);

								item = cJSON_GetObjectItem(net_cfg, "gateway");
								if(item != NULL)
									cJSON_AddStringToObject(net_info, "gateway",item->valuestring);

								item = cJSON_GetObjectItem(net_cfg, "dns");
								if(item != NULL)
									cJSON_AddStringToObject(net_info, "dns",item->valuestring);

								cJSON_Delete(net_cfg);
							}

							cJSON_AddItemToObject(root, "network", net_info);
						}

						char *out = cJSON_PrintUnformatted(root);
						membuf_write(writer, out, strlen(out)+1);
						free(out);
						cJSON_Delete(root);
					}
				}
				break;

				case CMD_BATCH_SET_DS_INFO: 	// 批量设置, 除P2P_DEVICE_INFO中的p2pid其它的参数生效
				case CMD_SET_DS_INFO: {	// 设置设备信息

					P2P_DEVICE_INFO	*ptMsgData = (P2P_DEVICE_INFO *)membuf_get_current(reader);

					if (ptMsgData EQU NULL) {
						LOGE("CMD_SET_DS_INFO error 1");
						continue;
					}

					g_pEmbedInfo->dev_type 		= ptMsgData->devType;
					g_pEmbedInfo->panorama_mode	= ptMsgData->panoramaMode;
					g_pEmbedInfo->language		= ptMsgData->language;
					g_pEmbedInfo->odm			= ptMsgData->odmID;
					strcpy(g_pEmbedInfo->model, ptMsgData->model);

					// 批量时不能改p2p号
					if (ptPackHead->dwCmd != CMD_BATCH_SET_DS_INFO) {
						strcpy(g_pEmbedInfo->p2pid, ptMsgData->p2pid);
					}

					// 全景或者带有扩展数据的
					if (sizeof(P2P_DEVICE_INFO) EQU membuf_remain_size(reader)) {
						memcpy(g_pEmbedInfo->my_key, ptMsgData->my_key, QfLength(g_pEmbedInfo->my_key));
					}

					if (save_embed_info(g_pEmbedInfo, NULL)) {
						LOGI("save_embed_info() succ");
					} else {
						LOGE("save_embed_info() fail");
					}
				}
				break;

				// WiFi functionality removed - dual Ethernet mode only

				case CMD_LICENSE:				// 普通授权模式
				case CMD_LICENSE_FORCE:{		// 普通授权模式
					if (strncmp((char*)(membuf_get_current(reader)), VS_LICENSE_FLAG, strlen(VS_LICENSE_FLAG)) EQU 0) {
						UINT32 	lic_type = ptPackHead->dwCmd - CMD_LICENSE + 1;
						CHAR	lic_ip[64];
						LPCSTR	svr_ip = (LPCSTR)membuf_get_current(reader)+strlen(VS_LICENSE_FLAG)+1;

						if (!IS_VALID_ID(g_pEmbedInfo->p2pid))
							lic_type = 1;

						QfSet0(lic_ip, sizeof(lic_ip));
						if (isdigit(svr_ip[0])) {
							s_strcpy(lic_ip, svr_ip);
						}
						else {
							s_strcpy(lic_ip, inet_ntoa(rmtAddr.sin_addr));
						}
						factory_fuc(FFUNC_LICENSE, (LPVOID)lic_type, lic_ip);
					}
					else {
						LOGE("CMD_LICENSE(code=%u) from_ip=%s", ptPackHead->dwCmd, inet_ntoa(rmtAddr.sin_addr));
					}
				}
				break;

				case CMD_JSON_REQUET: {	// json请求
					net_srch_json((LPCSTR)(membuf_get_current(reader)));
				}
				break;

				}

				if (membuf_get_size(writer) > 0) {

					sockaddr_in toAddr;

					toAddr.sin_family 		= AF_INET;
					toAddr.sin_port 		= htons(VS_PORT_SRCH_DATA);
					//toAddr.sin_addr.s_addr = inet_addr(getBCAddr().c_str());	// 只能在广播域网才可以接收到数据
					toAddr.sin_addr.s_addr 	= inet_addr(QFPH_MULTI_ADDR);
					fnRet = sendto(scktFd, membuf_get_pointer(writer), membuf_get_size(writer), 0, (struct sockaddr *)&toAddr, sizeof(toAddr));
					if (fnRet < 0) {
						LOGE("sendto(%s:%d, len=%d)=%d, err=%d", inet_ntoa(toAddr.sin_addr), ntohs(toAddr.sin_port), membuf_get_size(writer), fnRet, errno);
					}

					// 避免部分安卓机无法收到数据
					fnRet = sendto(scktFd, membuf_get_pointer(writer), membuf_get_size(writer), 0, (struct sockaddr *)&rmtAddr, sizeof(rmtAddr));
//					LOGI("sendto(%s:%d, len=%d)=%d", inet_ntoa(rmtAddr.sin_addr), ntohs(rmtAddr.sin_port), membuf_get_size(writer), fnRet);
				}

			}
		}
	}
	close(scktFd);
	free(data);
	membuf_close(&reader);
	membuf_close(&writer);

	END_THREAD;

	return NULL;
}


/**
 * 关于网络状态的改变,
 *	1.有线网络检测非常准确,也很真实,上线与下线比较容易区分,所以检测到网络掉线后,
 		可以直接切换到wifi模式
 *	2.wifi的切换是在有线断开的前提下,或是一开始就没有插有线的情况下
 		无线的切换需要先加载配置,但是wifi连接到就绪需要很长时间,所以不能在加载配置以后就去获取IP
 		wifi的就绪我以读取到ip为准,wifi从设置ssid到真正连接ok,都会有事件持续来
 */

// 网络状态改变
VOID on_if_state(LPCSTR if_name, UINT8 up, INT32 *save_state, INT32 *delay_ms_to_eth1)
{
	*delay_ms_to_eth1 = 0;
	if (system_quit()) return;

	LOGI("%s() %s=%d, last state=%d", __FUNCTION__, if_name, up, *save_state);

	// 网络接口状态管理

	static UINT32 last_state_change_time = 0;
	UINT32 current_time = get_app_uptime();

	// 防止状态抖动 - 至少间隔2秒
	if (current_time - last_state_change_time < 2) {
		LogI("State change too frequent, ignoring");
		return;
	}

	if (!up) {
		net_handle_interface_event(if_name, FALSE, save_state, current_time, &last_state_change_time);
	}
	else {
		net_handle_interface_event(if_name, TRUE, save_state, current_time, &last_state_change_time);
	}
	if (up) {
		net_handle_interface_configuration(if_name);
	} else if (stricmp(if_name, NET_ETH0) == 0 && net_dev_exist(NET_ETH1)) {
		// ETH0断开时的故障转移
		*delay_ms_to_eth1 = 4;
		LOGI("Scheduled failover to ETH1 in %d seconds", *delay_ms_to_eth1);
	}

	// 记录网络状态变化
	LOGI("Network state changed: %s %s, new state=%d",
		 if_name, up ? "UP" : "DOWN", *save_state);
}

INT32 Random(INT32 start, INT32 end){
    INT32 dis = end - start;

    return rand() % dis + start;
}


/**
 * 应用有线网卡地址
 * @return   成功=TRUE；失败=FALSE
 */
UINT8 net_apply_net_mac()
{
	CHAR 	tmp[512];
	CHAR 	mac_value[64] = {0};

	QfSet0(mac_value, sizeof(mac_value));
	do {

		// 已经有mac地址了
		if (strlen(g_pEmbedInfo->eth0_mac) > 0) {
			s_strcpy(mac_value, g_pEmbedInfo->eth0_mac);
			break;
		}

		//==========================================
		// 自己生成mac
		//==========================================

		// 根据芯片id产生一个
		{
			CHAR chip_id[64];
			extern UINT8 vsipc_chipid_mac(CHAR *chip_id,  CHAR *mac_value);

			if (vsipc_chipid_mac(chip_id, mac_value))
				break;
		}


		// step1. 获取eth1的mac地址用于eth0
		if (!dev_lte_exist() && net_dev_exist(NET_ETH1) && net_if_mac_inc1(NET_ETH1, mac_value, NULL)) {		// 如果有eth1，就使用eth1的mac+1
			if (stricmp(g_pEmbedInfo->eth0_mac, mac_value) != 0) {
				LOGW("modify eth0 mac, [%s] to [%s]", g_pEmbedInfo->eth0_mac, mac_value);
				strcpy(g_pEmbedInfo->eth0_mac, mac_value);
				save_embed_info(g_pEmbedInfo, NULL);
			}
		}
		// step2, 在p2p地址有效的情况下, 使用p2p号第9位后6字节
		else if (stricmp(g_pEmbedInfo->p2pid, INVALID_P2P_ID) != 0) {
			CHAR	*mac_tmp;

			mac_tmp = mac_value;
			for (int i = 0; i < 6; i++) {
				mac_tmp += sprintf(mac_tmp, "%02x:", g_pEmbedInfo->p2pid[8+i]&0xFE);
			}
			*(mac_tmp-1) = 0;
			//LOGI("%s()111 eth0 mac: %s", __func__, mac_value);
		}

#if 0
		// 一般不需要随机产生,批量上电的时候摄像机启动的时候,这些值都是一样的

		// 如果mac地址没有处理处理,就随机产生一个
		if (strlen(mac_value) EQU 0) {
			INT32	i;
			CHAR	*mac_tmp;

			mac_tmp = mac_value;
			srand((unsigned)utc_time());
			for (i = 0; i < 6; i++) {
				mac_tmp += sprintf(mac_tmp, "%02x:", Random(0, 255)&0xFE);
			}
			*(mac_tmp-1) = 0;
			//LOGI("%s()222 eth0 mac: %s", __func__, mac_value);
		}
#endif
	}while (FALSE);

	LOGI("%s() eth0 mac_value: %s, ethx mac_value: %s, work mac: %s", __func__, g_pEmbedInfo->eth0_mac, g_pEmbedInfo->ethx_mac, mac_value);

	// 修改mac的指令
	char *pos = tmp;

	if (net_dev_exist(NET_ETH0) && strlen(mac_value) > 0) {
		//pos += sprintf(pos, "ifconfig " NET_ETH0 " down;");
		pos += sprintf(pos, "ifconfig %s hw ether %s;", NET_ETH0, mac_value);
		//pos += sprintf(pos, "ifconfig " NET_ETH0 " up;");
	}

	// 设置eth1的MAC地址（如果存在）
	if (net_dev_exist(NET_ETH1)) {
		if (dev_lte_exist()) {
			pos += sprintf(pos, "ifconfig %s hw ether %s;", NET_ETH1, mac_value);
		}
		else {
			if ((UINT8)g_pEmbedInfo->ethx_mac[0] != 0xff && strlen(g_pEmbedInfo->ethx_mac) > 0) {
				if (pos EQU tmp)
					pos += sprintf(pos, "ifconfig %s hw ether %s;", NET_ETH1, g_pEmbedInfo->ethx_mac);
				else
					pos += sprintf(pos, "ifconfig %s hw ether %s;", NET_ETH1, g_pEmbedInfo->ethx_mac);
			}
		}
	}

	if (pos EQU tmp)
		return FALSE;

	return system_run(tmp) EQU 0;
}

/**
 * 网关接口智能赋值机制
 * 根据网络接口可用性和配置文件，智能选择网关接口
 * @return 成功返回TRUE，失败返回FALSE
 */
static UINT8 net_init_gateway_interface_assignment()
{
	CHAR loaded_gateway[128] = {0};

	LOGI("Gateway Interface Initialization: Starting intelligent assignment...");

	// 初始化网关接口变量
	QfSet0(g_manual_gateway_interface, sizeof(g_manual_gateway_interface));

	// 第一步：检测网络接口的物理连接状态（网线插入状态）
	UINT8 eth0_carrier = net_dev_carrier(NET_ETH0);
	UINT8 eth1_carrier = net_dev_carrier(NET_ETH1);

	LOGI("Physical connection status: eth0_carrier = %d, eth1_carrier = %d", eth0_carrier, eth1_carrier);

	// 第二步：尝试从配置文件加载已保存的网关接口设置
	UINT8 config_loaded = FALSE;
	UINT8 config_valid = FALSE;

	if (settings_load_net_wan(loaded_gateway) && strlen(loaded_gateway) > 0) {
		config_loaded = TRUE;

		// 验证加载的接口名称是否有效且接口有物理连接
		if (stricmp(loaded_gateway, NET_ETH0) == 0 && eth0_carrier) {
			config_valid = TRUE;
		} else if (stricmp(loaded_gateway, NET_ETH1) == 0 && eth1_carrier) {
			config_valid = TRUE;
		}
	}
	else {
		LOGI("Gateway Interface Config: No saved configuration found");
	}

	// 第三步：基于物理连接状态的智能赋值逻辑
	if (eth0_carrier && eth1_carrier) {
		// 双网口都有物理连接：优先使用配置文件，默认使用ETH0
		if (config_valid) {
			strcpy(g_manual_gateway_interface, loaded_gateway);
			LOGI("Gateway Interface Assignment: Using valid saved config '%s' (both interfaces connected)", loaded_gateway);
		}
		else {
			strcpy(g_manual_gateway_interface, NET_ETH0);
			LOGI("Gateway Interface Assignment: Both interfaces connected, defaulting to ETH0 (carrier-based policy)");
		}
	}
	else if (eth0_carrier && !eth1_carrier) {
		// 只有ETH0有物理连接
		strcpy(g_manual_gateway_interface, NET_ETH0);
		LOGI("Gateway Interface Assignment: Only ETH0 has physical connection (carrier-based policy)");
	}
	else if (!eth0_carrier && eth1_carrier) {
		// 只有ETH1有物理连接
		strcpy(g_manual_gateway_interface, NET_ETH1);
		LOGI("Gateway Interface Assignment: Only ETH1 has physical connection (carrier-based policy)");

		// 保存ETH1为网关接口（因为它是唯一有连接的接口）
		settings_save_net_wan(g_manual_gateway_interface);
		LOGI("Gateway Interface Assignment: Saved ETH1 as gateway interface (only connected interface)");
	}
	else {
		// 无物理连接的网络接口
		QfSet0(g_manual_gateway_interface, sizeof(g_manual_gateway_interface));
		LOGI("Gateway Interface Assignment: No interfaces have physical connection");
	}

	LOGW("Physical connection status: eth0_carrier = %d, eth1_carrier = %d, loaded_config = '%s', selected_gateway_interface = '%s'",
		 eth0_carrier, eth1_carrier, loaded_gateway, g_manual_gateway_interface);

	return TRUE;
}

/**
 * net初始化
 * @return   成功=OK；失败=FAIL
 */
INT32 net_init()
{
	INT32	scktFd;
	INT32 	iFnRet;
	INT32 	sckt_value;
	struct sockaddr_in tSockAddr;

	//==================================================
	// 网络搜索
	//==================================================

	scktFd = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
 	if (scktFd < 0) {
		LOGE("socket() error=%d", errno);
		return FAIL;
	}

	INT32 iReuseAddress = 1;
	iFnRet = setsockopt(scktFd, SOL_SOCKET, SO_REUSEADDR,
						(char *)&iReuseAddress, sizeof(iReuseAddress));
	if (SOCKET_ERROR EQU iFnRet) {
		LOGE("setsockopt() error=%d", errno);
		goto FN_END;
	}

	tSockAddr.sin_port   = htons(VS_PORT_SRCH_SVR);
	tSockAddr.sin_family = AF_INET;
	tSockAddr.sin_addr.s_addr = INADDR_ANY;
	iFnRet = bind(scktFd, (struct sockaddr*)&tSockAddr, sizeof(tSockAddr));
	if (iFnRet EQU SOCKET_ERROR) {
		LOGE("bind() error=%d", errno);
		goto FN_END;
	}

	// 广播
	sckt_value = TRUE ;
	iFnRet = setsockopt(scktFd, SOL_SOCKET, SO_BROADCAST, (char *)&sckt_value, sizeof(sckt_value));
	if (SOCKET_ERROR EQU iFnRet) {
		LOGE("setsockopt(SO_BROADCAST) error=%d", errno);
	}

	// loopback
	sckt_value = FALSE;
	iFnRet = setsockopt(scktFd, SOL_SOCKET, IP_MULTICAST_LOOP, (char *)&sckt_value, sizeof(sckt_value));
	if (SOCKET_ERROR EQU iFnRet) {
		LOGE("setsockopt(IP_MULTICAST_LOOP) error=%d", errno);
	}

	// thread
	PTHREAD_CREATE(&g_workThread, NULL, net_srch_cmd_thread, (LPVOID)scktFd);

	// 执行网关接口智能赋值
	if (!net_init_gateway_interface_assignment()) {
		LOGE("Gateway interface assignment failed, continuing with default policy");
	}

	// 启动时自动IP配置
	LOGI("Starting simplified startup auto IP configuration...");

	// 在单独的线程中执行自动配置
	pthread_t startup_config_thread;
	if (PTHREAD_CREATE(&startup_config_thread, NULL, net_simplified_startup_thread, NULL) == 0) {
		LOGI("Simplified startup auto IP configuration thread created");
		pthread_detach(startup_config_thread);  // 分离线程，自动清理
	} else {
		LOGE("Failed to create simplified startup auto IP configuration thread");
		// 如果线程创建失败，直接执行配置
		net_simplified_auto_config();
	}

	LOGI("net module init.");

	return OK;

FN_END:
	close(scktFd);

	return FAIL;

}

/**
 * net反初始化
 */
VOID net_uninit()
{
	WAIT_THREAD(g_workThread, NULL);

	net_ifdetect_stop();

	LOGI("net module uninit.");
}

/**
 * 判断网络设备是否存在
 * @return   返回1表示存在；否返回0
 */
UINT8 net_dev_exist(LPCSTR if_name)
{
	CHAR	dev_path[64] = {0};

	_snprintf(dev_path, sizeof(dev_path)-1, "/sys/class/net/%s", if_name);

	return dir_exist(dev_path);
}

/**
 * 判断网络设备物理连接是否可用, 但是可能由于dhcp或者ip冲突不可用
 *	有线网络网线已经插入
 *	wifi必须已经配上ssid并连接上
 * @return   返回1表示可用；否返回0
 */
UINT8 net_dev_carrier(LPCSTR if_name)
{
	INT32	fd;
	CHAR	dev_path[64] = {0};

	_snprintf(dev_path, sizeof(dev_path)-1, "/sys/class/net/%s/carrier", if_name);

	fd= open(dev_path, O_RDONLY);
	if (fd > 0) {
		UINT8	result = FALSE;

		if (read(fd, dev_path, sizeof(dev_path)) > 0) {
			if (atoi(dev_path) > 0) {
				result = TRUE;
			}
		}
		close(fd);

		return result;
	}

	return FALSE;
}



/**
 * 判断网络是否运行并处理就绪状态
 * 	就绪时,如果ip!=NULL,将ip地址存储在里面
 * @return   返回1表示就绪；否返回0
 */
UINT8 net_if_ready(LPCSTR if_name, LPSTR ip)
{
	int skfd = 0;
    struct ifreq ifr;

	// 初始化串
	if (ip) ip[0] = 0;

	// 判断设备是否存在
	if (if_name EQU NULL || !net_dev_exist(if_name))
		return FALSE;

    skfd = socket(AF_INET, SOCK_DGRAM, 0);
    if(skfd < 0)
    {
        LOGE("Open socket error, errno = %d!\n", errno);
        return FALSE;
    }

    strcpy(ifr.ifr_name, if_name);
#if 1
	do {
		// 检测网卡状态
		if (ioctl(skfd, SIOCGIFFLAGS, &ifr) < 0) {
	        LOGE("Maybe ethernet inferface %s is not valid! errno=%d\n",  if_name, errno);
	        break;
	    }
		if (ifr.ifr_flags & IFF_RUNNING) {
			int i;

			for (i = 0; i < MAX_TRY_COUNT; i++) {
				if (ioctl(skfd, SIOCGIFADDR, &ifr) < 0) {
//			        LOGE("%s SIOCGIFADDR IOCTL error! errno=%d\n",  if_name, errno);
					Sleep(1000);
					continue;
			    }
				break;
			}
			if (i EQU MAX_TRY_COUNT)
				break;

		    struct sockaddr_in *pAddr = (struct sockaddr_in *)&(ifr.ifr_addr);
			//LOGI("%s ip addr :[%s]", if_name, inet_ntoa(pAddr->sin_addr));
			if (pAddr->sin_addr.s_addr EQU 0) {	// 检测是否得到IP
				break;
			}

			if (ip != NULL) strcpy(ip, inet_ntoa(pAddr->sin_addr));

			close(skfd);

			return TRUE;
		}
	}while (0);
	close(skfd);

	return  FALSE;
#else
	// 检测网络是否通(但是wifi下会有问题)
	struct ethtool_value edata;
	edata.cmd = ETHTOOL_GLINK;
	edata.data = 0;
	ifr.ifr_data = (char *) &edata;
	if (ioctl(skfd, SIOCETHTOOL, &ifr) < 0) {

		LOGE("Maybe ethernet inferface %s is not valid!errno=%d", if_name, errno); // wifi则不行
        close(skfd);
        return FALSE;
    }

	close(skfd);
	fprintf(stdout, "%s Link detected: %s\n",
                    if_name, edata.data ? "yes":"no");

    return (edata.data) ? TRUE : FALSE;
#endif
}


/**
 * 得到指定网络设备的MTU
 * @return   返回>0表示成功; 否则-1
 */
INT32 net_get_mtu(LPCSTR if_name)
{
	int skfd = 0;
	struct ifreq ifr;

	// 判断设备是否存在
	if (if_name EQU NULL || !net_dev_exist(if_name))
		return -1;

	skfd = socket(AF_INET, SOCK_DGRAM, 0);
	if(skfd < 0)
	{
		LOGE("Open socket error!");
		return -1;
	}

	strcpy(ifr.ifr_name, if_name);
	if (ioctl(skfd, SIOCGIFMTU, &ifr) < 0) {
		close(skfd);
		LOGE("if=%s, get mtu error", if_name);
		return -1;
	}
	close(skfd);

	return ifr.ifr_mtu;

}

/**
 * 设置指定网络设备的MTU
 * @return   返回=0表示成功; 否则-1
 */
INT32 net_set_mtu(LPCSTR if_name, INT32 mtu_size)
{
	int skfd = 0;
	struct ifreq ifr;

	// 判断设备是否存在
	if (if_name EQU NULL || !net_dev_exist(if_name))
		return -1;

	skfd = socket(AF_INET, SOCK_DGRAM, 0);
	if(skfd < 0)
	{
		LOGE("Open socket error!");
		return -1;
	}

	strcpy(ifr.ifr_name, if_name);
	ifr.ifr_mtu = mtu_size;
	if (ioctl(skfd, SIOCSIFMTU, &ifr) < 0) {
		close(skfd);
		LOGE("if=%s, mtu=%d, set mtu error", if_name, mtu_size);
		return -1;
	}
	close(skfd);

	return 0;

}


/**
 * 得到网关地址
 * if_name为空时,将获取默认网关地址,成功,将ip地址存储在里面
 * @return   返回1表示就绪；否返回0
 */
UINT8 net_gw_addr(LPCSTR if_name, LPCHAR ip)
{
#if 0
	CHAR	cmdline[120];
	CHAR	readline[300];
	FILE	*fp;

	if (if_name) {
		sprintf(cmdline, "ip route|grep %s|grep default|cut -d ' ' -f 3", if_name);
	}
	else {
		strcpy(cmdline, "ip route|grep default|cut -d ' ' -f 3");
	}
	*ip = 0;

	fp = popen(cmdline, "r");
	if (fp EQU NULL)
		return FALSE;

	while ( NULL != fgets(readline, sizeof(readline), fp)) {
		strncpy(ip, readline, strlen(readline)-1);		// "\n"
		break;
	}
	pclose(fp);

	return *ip != 0 ? TRUE : FALSE;
#else
	FILE *pfile = fopen("/proc/net/route", "r");

	*ip = 0;

    if (pfile != NULL){
		char	*line = NULL;
		size_t	nums = 0;
	 	char 	iface[32];
		UINT32 	dest_addr=0, gate_addr=0;

		while (getline(&line, &nums, pfile) != -1){

			if (sscanf(line, "%s\t%x\t%x", iface, &dest_addr, &gate_addr) EQU 3
				&& dest_addr EQU 0
				&& gate_addr != 0) {
//				LogD("GateWay iface=%s", iface);
				if (!if_name || strncmp(if_name, iface, strlen(if_name)) EQU 0){
		            sprintf(ip, "%hhu.%hhu.%hhu.%hhu",
		            		gate_addr&0xff, (gate_addr>>8)&0xff,
		            		(gate_addr>>16)&0xff, (gate_addr>>24)&0xff);
//					LogD("GateWay ip=%s", ip);
					free(line);
					fclose(pfile);
					return TRUE;
				}
			}

			free(line);
			line = NULL;
			nums = 0;
		}
		fclose(pfile);
    }

	return FALSE;
#endif


}

/**
 * 判断tcp是否严重拥堵
 * @return  返回拥堵量: -1(表示没有连接), 0-10
 */
INT32 net_tcp_jam()
{
	UINT32	total=0, retry = 0;
	UINT32 	no;
	UINT32 	local_port;
	UINT32 	rem_port;
	UINT32 	status;
	UINT32 	tx_queue;
	UINT32 	rx_queue;
	UINT32	tr,tm_when,retrnsmt;
	FILE	*pfile;
	LPCSTR	tcps[] = {"/proc/net/tcp", "/proc/net/tcp6"};

	for (int i = 0; i < QfLength(tcps); i++){
		pfile = fopen(tcps[i], "r");
	    if (pfile != NULL){
			char	*line = NULL;
			size_t	nums = 0;

			while (getline(&line, &nums, pfile) != -1){

				if (sscanf(line, "%u: %*[^:]:%x %*[^:]:%x %x %x:%x %x:%x %x\n",
		                  &no, &local_port, &rem_port,
		                  &status, &tx_queue, &rx_queue,
		                  &tr, &tm_when, &retrnsmt) EQU 9
					&& status EQU TCP_ESTABLISHED) {

					total++;
					if (retrnsmt > 0)
						retry++;
				}

				free(line);
				line = NULL;
				nums = 0;
			}
			fclose(pfile);
	    }
	}

	if (total EQU 0)
		return -1;

	return retry*10/total;
}


/**
 * 得到网卡mac地址
 * @return   返回1表示就绪；否返回0
 */
UINT8 net_if_mac(LPCSTR if_name, LPCHAR mac, LPCSTR fmt)
{
	int skfd = 0;
	struct ifreq ifr;

	// 初始化串
	if (mac) mac[0] = 0;

	skfd = socket(AF_INET, SOCK_DGRAM, 0);
	if(skfd < 0)
	{
		LOGE("Open socket error!\n");
		return FALSE;
	}

	strcpy(ifr.ifr_name, if_name);
	do {

		// 检测网卡状态
		if (ioctl(skfd, SIOCGIFFLAGS, &ifr) < 0) {
			LOGE("Maybe ethernet inferface %s is not valid! errno=%d\n",  if_name, errno);
			break;
		}

//		if (ifr.ifr_flags & IFF_RUNNING) {
			int i;

			for (i = 0; i < MAX_TRY_COUNT; i++) {
				if (ioctl(skfd, SIOCGIFHWADDR,  &ifr) < 0) {
//					LOGE("%s SIOCGIFADDR IOCTL error! errno=%d\n",	if_name, errno);
					Sleep(1000);
					continue;
				}
				break;
			}
			if (i EQU MAX_TRY_COUNT)
				break;

			if (mac != NULL) {

                sprintf(mac, fmt EQU NULL ? "%02x:%02x:%02x:%02x:%02x:%02x" : fmt,
                    (UINT8)ifr.ifr_hwaddr.sa_data[0],
                    (UINT8)ifr.ifr_hwaddr.sa_data[1],
                    (UINT8)ifr.ifr_hwaddr.sa_data[2],
                    (UINT8)ifr.ifr_hwaddr.sa_data[3],
                    (UINT8)ifr.ifr_hwaddr.sa_data[4],
                    (UINT8)ifr.ifr_hwaddr.sa_data[5]);
			}

			close(skfd);

			return TRUE;
		//}
	}while (0);
	close(skfd);

	return	FALSE;

}


UINT8 net_if_mac_inc1(LPCSTR if_name, LPCHAR mac, LPCSTR fmt)
{
	int skfd = 0;
	struct ifreq ifr;

	// 初始化串
	if (mac) mac[0] = 0;

	skfd = socket(AF_INET, SOCK_DGRAM, 0);
	if (skfd < 0)
	{
		LOGE("Open socket error!\n");
		return FALSE;
	}

	strcpy(ifr.ifr_name, if_name);
	do {

		// 检测网卡状态
		if (ioctl(skfd, SIOCGIFFLAGS, &ifr) < 0) {
			LOGE("Maybe ethernet inferface %s is not valid! errno=%d\n",  if_name, errno);
			break;
		}

//		if (ifr.ifr_flags & IFF_RUNNING) {
			int i;

			for (i = 0; i < MAX_TRY_COUNT; i++) {
				if (ioctl(skfd, SIOCGIFHWADDR,  &ifr) < 0) {
//					LOGE("%s SIOCGIFADDR IOCTL error! errno=%d\n",	if_name, errno);
					Sleep(1000);
					continue;
				}
				break;
			}
			if (i EQU MAX_TRY_COUNT)
				break;

			if (mac != NULL) {

                sprintf(mac, fmt EQU NULL ? "%02x:%02x:%02x:%02x:%02x:%02x" : fmt,
                    (UINT8)ifr.ifr_hwaddr.sa_data[0],
                    (UINT8)ifr.ifr_hwaddr.sa_data[1],
                    (UINT8)ifr.ifr_hwaddr.sa_data[2],
                    (UINT8)(ifr.ifr_hwaddr.sa_data[3]+1),
                    (UINT8)ifr.ifr_hwaddr.sa_data[4],
                    (UINT8)ifr.ifr_hwaddr.sa_data[5]);
			}

			close(skfd);

			return TRUE;
		//}
	}while (0);
	close(skfd);

	return	FALSE;

}

/**
 * 得到有效的mac地址,检测步骤是从NET_ETH1=>g_pEmbedInfo->ethx_mac=>NET_ETH0=>g_pEmbedInfo->eth0_mac
 *
 * @return   返回1表示就绪；否返回0
 */
UINT8 net_valid_if_mac(LPCSTR if_name, LPCHAR mac, LPCSTR fmt)
{

	// eth1
	do {
		if (stricmp(if_name, NET_ETH1) EQU 0) {

			if (strlen(g_pEmbedInfo->ethx_mac) EQU 0) {
				if (net_dev_exist(NET_ETH1))
					return net_if_mac(if_name, mac, fmt);
				break;
			}

			return net_parse_mac(g_pEmbedInfo->ethx_mac, mac, fmt);
		}

	} while (FALSE);

	do {
		//if (stricmp(if_name, NET_ETH0) EQU 0) {

			//LOGI("%s() NET_ETH0:2222 %s", __func__, g_pEmbedInfo->eth0_mac);
			if (strlen(g_pEmbedInfo->eth0_mac) EQU 0) {
				//LOGI("%s() NET_ETH0:11111 %s", __func__, g_pEmbedInfo->eth0_mac);
				if (net_dev_exist(NET_ETH0))
					return net_if_mac(NET_ETH0, mac, fmt);
				break;
			}

			//LOGI("%s() NET_ETH0: %s", __func__, g_pEmbedInfo->eth0_mac);
			return net_parse_mac(g_pEmbedInfo->eth0_mac, mac, fmt);
		//}

	} while (FALSE);

	return FALSE;
}


/**
 * 解析MAC地址
 * @param  src_mac  源mac地址
 * @param  mac  返回的MAC地址
 * @param  fmt=NULL  格式
 * @return  返回1表示就绪；否返回0
 */
UINT8 net_parse_mac(LPCSTR src_mac, LPCHAR mac, LPCSTR fmt)
{
	UINT32	mac6[6];

	// 初始化串
	if (mac) mac[0] = 0;

	QfSet0(mac6, sizeof(mac6));
    sscanf(src_mac, "%02x:%02x:%02x:%02x:%02x:%02x",
           &mac6[0],
           &mac6[1],
           &mac6[2],
           &mac6[3],
           &mac6[4],
           &mac6[5]);
	if (mac != NULL) {
        sprintf(mac, fmt EQU NULL ? "%02x:%02x:%02x:%02x:%02x:%02x" : fmt,
            (UINT8)mac6[0],
            (UINT8)mac6[1],
            (UINT8)mac6[2],
            (UINT8)mac6[3],
            (UINT8)mac6[4],
            (UINT8)mac6[5]);

		return TRUE;
	}

	return FALSE;
}


/**
 * 网络异常判断(只判断非有线网络)
 */
UINT8 network_abnormal()
{
	// 有线网络不做判断
	if (stricmp(net_get_work_ifname(), NET_ETH0) != 0) {
		UINT32 	t_pkts[2], r_pkts[2], t_drop[2], r_drop[2], carr;

		for (INT32 i = 0; i < 2; i++) {
			if (!net_dev_status(net_get_work_ifname(), &t_pkts[i], &r_pkts[i], &t_drop[i], &r_drop[i], &carr))	{
				LOGE_NF("net_get_status[%d] fail", i);
				return TRUE;
			}
			if (i EQU 0){
				Sleep(2000);
			}
		}

		if (t_pkts[0] EQU t_pkts[1]
			&& r_pkts[0] EQU r_pkts[1]) {
			LOGW_NF("net_get_status, r_pkts=%u, t_pkts=%u", r_pkts[0], t_pkts[0]);
			return TRUE;
		}

	}

	return FALSE;
}


#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wsign-compare"
LPVOID if_detect_thread(LPVOID)
{
	THREAD_FUNC_BEGIN();

	UINT8	loop = 1;
	INT32 	fnRet;
	ULONG	lose_net_time = 0;
	UINT8	net_except = FALSE;
    CHAR 	*buf = (CHAR *)malloc(BUFLEN);
	CHAR	ip[32];
    INT32 	len;
	fd_set		read_fds;
	timeval 	tv;
	struct nlmsghdr 	*nh;
    struct ifinfomsg 	*ifinfo;
    struct rtattr 		*attr;
	INT32	delay_ms_to_eth1 = 0;

	// vs_logdb_set_env(VS_SOURCE_SYS, IPC_MOD_NET);
	while (loop &&
	 		g_fd_detect &&
	 		!system_quit())
	{
#ifdef NET_ABNORMAL_REBOOT_IPC
		// 脱网将重启
		if (net_except
			&& network_abnormal()
			&& !g_pRunSets->misc_set.disable_reboot
			&& get_app_uptime() - lose_net_time > NET_ABNORMAL_TIMEOUT_INTERFACE) {
			LOGI("if_detect_thread(). reboot");
			set_except_flag();
			system_reboot();
			break;
		}
#endif

		wdt_live();

   		FD_ZERO(&read_fds);
		FD_SET(g_fd_detect, &read_fds);
		tv.tv_sec 	= 1; // 超时
		tv.tv_usec 	= 0;
		fnRet = ::select(g_fd_detect+1, &read_fds, NULL, NULL, &tv);//检测socket是否有效
		if (fnRet < 0) {

			if (errno EQU EINTR)
				continue;

			LogW("select(). errno=%d", errno);
			break;
		}

		if (fnRet EQU 0 ||
			!FD_ISSET(g_fd_detect, &read_fds)) {

			//LOGI("if_detect_thread(). state: %d", g_if_save_state);

			// 检测网络状态
			static UINT32 last_quality_check = 0;
			UINT32 current_time = get_app_uptime();



			if (g_if_save_state EQU NET_ST_NONE) {

				// 处理延时问题
				if (delay_ms_to_eth1) {
					LOGI_NF(" delay=%d, state=%d", delay_ms_to_eth1, g_if_save_state);
					if (--delay_ms_to_eth1 EQU 0) {
						net_load_config(NET_ETH1);
						// 延迟配置不立即保存，等IP获取成功后再保存
					}
					continue;
				}

				// 使用智能网络模式适应
				INT32 new_state = net_dual_eth_load_balance();

				if (new_state != NET_ST_NONE) {
					sync_time(TIME_CLOCK_SVR, FALSE);
					g_if_save_state = new_state;
					net_except = FALSE;

					// 根据状态记录日志
					switch (new_state) {
						case NET_ST_DUAL_ETH:
							LOGI("Dual Ethernet mode active");
							break;
						case NET_ST_ETH0:
							LOGI("ETH0 connection active");
							break;
						case NET_ST_ETH1:
							LOGI("ETH1 connection active");
							break;
					}
				}
				else {
					LOGE("[%s] net_except - no ethernet interface available",  net_get_work_ifname());
					net_except = TRUE;
					if (lose_net_time EQU 0){
						lose_net_time = get_app_uptime();
						// LOGDB_ERR(ETHERNET_DISCONNECTED);
					}
				}

			}
			else {
				// 稳定的网络状态检查 (每60秒，增加稳定性验证)
				if (current_time - last_quality_check > 60) {  // 增加检查间隔，减少干扰
					last_quality_check = current_time;

					// 基本的网络状态检查（增加稳定性验证）
					{
						UINT8 eth0_ready = net_if_ready(NET_ETH0, NULL);
						UINT8 eth1_ready = net_if_ready(NET_ETH1, NULL);
						UINT8 eth0_carrier = net_dev_carrier(NET_ETH0);
						UINT8 eth1_carrier = net_dev_carrier(NET_ETH1);

						LOGI("Network status check: ETH0=%d(carrier=%d), ETH1=%d(carrier=%d)",
							 eth0_ready, eth0_carrier, eth1_ready, eth1_carrier);

						// 简单的故障转移逻辑（基于物理连接状态）
						LPCSTR current_if = net_get_work_ifname();
						UINT8 eth0_carrier = net_dev_carrier(NET_ETH0);
						UINT8 eth1_carrier = net_dev_carrier(NET_ETH1);

						// 只有在物理连接断开且目标接口有物理连接时才进行故障转移
						if (stricmp(current_if, NET_ETH0) == 0 && !eth0_ready && !eth0_carrier && eth1_ready && eth1_carrier) {
							LOGI("ETH0 physically disconnected, switching to ETH1");
							net_load_config(NET_ETH1);
							g_if_save_state = NET_ST_ETH1;
						} else if (stricmp(current_if, NET_ETH1) == 0 && !eth1_ready && !eth1_carrier && eth0_ready && eth0_carrier) {
							LOGI("ETH1 physically disconnected, switching to ETH0");
							net_load_config(NET_ETH0);
							g_if_save_state = NET_ST_ETH0;
						}
					}
				}

				net_handle_network_state_monitoring(ip, &net_except, &lose_net_time);

				if (g_if_save_state == NET_ST_NONE && net_except) {
					continue;  // 网络异常时跳过后续处理
				}
			}

			continue;
		}

    	fnRet = read(g_fd_detect, buf, BUFLEN);
        for (nh = (struct nlmsghdr *)buf; NLMSG_OK(nh, fnRet); nh = NLMSG_NEXT(nh, fnRet))
        {
            if (nh->nlmsg_type == NLMSG_DONE)
                break;
            else if (nh->nlmsg_type == NLMSG_ERROR) {
				loop = 0;
				break;
			} else if (nh->nlmsg_type != RTM_NEWLINK)
                continue;

			ifinfo = (ifinfomsg*)NLMSG_DATA(nh);
            attr = (struct rtattr*)(((char*)nh) + NLMSG_SPACE(sizeof(*ifinfo)));
            len = nh->nlmsg_len - NLMSG_SPACE(sizeof(*ifinfo));

			for (; RTA_OK(attr, len); attr = RTA_NEXT(attr, len))
            {
                if (attr->rta_type == IFLA_IFNAME)
                {
//                	LOGI_NF("%s=%#x, change_mask=%#x, last state=%d",
//						(char*)RTA_DATA(attr), (UINT32)ifinfo->ifi_flags, (UINT32)ifinfo->ifi_change, g_if_save_state);
					on_if_state((char*)RTA_DATA(attr), (ifinfo->ifi_flags & IFF_LOWER_UP) ? 1 : 0, &g_if_save_state, &delay_ms_to_eth1);
                    goto fn_read_next;
                }
            }
        }

fn_read_next:
		;

    }
	free(buf);

	THREAD_FUNC_END();
}
#pragma GCC diagnostic pop

/**
 * 开启网络接口检测
 * @return   成功=OK；失败=FAIL
 */
INT32 net_ifdetect_start()
{
	INT32	len = BUFLEN;
	INT32	scktFd = socket(AF_NETLINK, SOCK_RAW, NETLINK_ROUTE);
	struct sockaddr_nl addr;

	if (scktFd < 0) {
		LOGE("socket() error=%d", errno);
		return FAIL;
	}

    setsockopt(scktFd, SOL_SOCKET, SO_RCVBUF, &len, sizeof(len));
    memset(&addr, 0, sizeof(addr));
    addr.nl_family = AF_NETLINK;
    addr.nl_groups = RTNLGRP_LINK;
    bind(scktFd, (struct sockaddr*)&addr, sizeof(addr));
	g_fd_detect = scktFd;

	// thread
	PTHREAD_CREATE(&g_ifd_thread, NULL, if_detect_thread, NULL);

	LOGI(__FUNCTION__);

	return OK;
}

/**
 * 停止网络接口检测
 */
VOID net_ifdetect_stop()
{
	LOGI(__FUNCTION__);

	if (g_fd_detect != 0) {
		close(g_fd_detect);
		g_fd_detect = 0;
	}

	WAIT_THREAD(g_ifd_thread, NULL);
}

/**
 * 网络截入配置
 * @param  if_name 网络名,如果为NULL则自动检测
 * @return         成功=OK；失败=FAIL
 */
static CHAR  g_cur_if_name[32];
INT32 net_load_config(LPCSTR if_name)
{

#if defined(LH_LWJ)
	return 0;
#endif


	UINT8	is_eth1 = FALSE;
	UINT8	dual_eth_mode = net_dev_exist(NET_ETH1) ? TRUE : FALSE;
	T_SET_NETWORK	*net = NULL;
	UINT8	wpa_supp = FALSE;

	FILE	*js_file = fopen(JS_NET_FILE, "w");
	if (js_file EQU NULL)
	{
		LOGE("%s, fopen(), failed, errcode=%d", __FUNCTION__, errno);
		return FAIL;
	}
	set_wpa_ready(FALSE);
	fprintf(js_file, "#! /bin/sh\n");
	// 释放内存
	//fprintf(js_file, "echo 3 > /proc/sys/vm/drop_caches;\n");

	// 动态网关管理：在配置开始前清理网关状态
	fprintf(js_file, "\n# === Dynamic Gateway Management Initialization ===\n");
	fprintf(js_file, "echo 'Starting network configuration for %s with gateway management'\n", if_name ? if_name : "auto");
	fprintf(js_file, "echo 'Current routing table before configuration:'\n");
	fprintf(js_file, "route -n | grep '^0.0.0.0' || echo 'No default gateways found'\n");

	LOGI("%s, if_name=%s", __FUNCTION__, if_name);

	// 简单检查：验证指定接口是否存在
	if (if_name && !net_dev_exist(if_name)) {
		LOGW("Network config: Interface %s does not exist, skipping configuration", if_name);
		fclose(js_file);
		return FAIL;
	}

	// 判断是否为明确指定的接口
	UINT8 explicit_interface = (if_name != NULL && strlen(if_name) > 0);

	if (explicit_interface) {
		// 明确指定接口：严格按照指定的网卡进行配置
		LOGI("Using explicitly specified interface: %s", if_name);

		// 仅进行基本的接口存在性检查
		if (!net_dev_exist(if_name)) {
			LOGE("Specified interface %s does not exist", if_name);
			return FAIL;
		}

		LOGI("Interface %s exists, proceeding with configuration", if_name);
	} else {
		// 自动检测模式：使用简化的物理连接检测
		LOGI("Auto-detecting interface based on physical connection");

		UINT8 eth0_carrier = net_dev_carrier(NET_ETH0);
		UINT8 eth1_carrier = net_dev_carrier(NET_ETH1);

		LOGI("Physical connection status: ETH0=%d, ETH1=%d", eth0_carrier, eth1_carrier);

		if (dev_lte_exist()) {	// 有4G模块的情况
			if (eth0_carrier || net_if_ready(NET_ETH0, NULL) || !g_pSystemSets->stat.lte_mode) {
				if_name = NET_ETH0;
			}
			else if (net_dev_exist(NET_ETH1) && eth1_carrier) {
				if_name = NET_ETH1;
			}
		}
		else {
			// 基于物理连接的简单选择逻辑
			if (eth0_carrier) {
				if_name = NET_ETH0;
			}
			else if (eth1_carrier) {
				if_name = NET_ETH1;
			}
			else {
				// 如果都没有物理连接，检查已配置的接口
				if (net_if_ready(NET_ETH0, NULL)) {
					if_name = NET_ETH0;
				}
				else if (dual_eth_mode && net_if_ready(NET_ETH1, NULL)) {
					if_name = NET_ETH1;
				}
			}
		}

		LOGI("Auto-selected interface: %s", if_name ? if_name : "none");
	}

	s_strcpy(g_cur_if_name, if_name);

	if (dev_lte_exist()) {	// 有4G模块的情况

		LOGI("lte_mode=%d, apn_name=%s",
			g_pSystemSets->stat.lte_mode, DEV_LTE_APN_NAME);

		// LTE functionality removed for dual Ethernet mode
	}


	if (!is_mount_mode()) {

		// 仅清除当前接口的IP地址，保护其他已配置接口
		fprintf(js_file, "ip addr flush dev %s;\n", if_name);

		// eth0网络
		if (stricmp(if_name, NET_ETH0) EQU 0) {
			net = &g_pRunSets->eth0;

			// 只清除当前接口的旧路由，不影响其他接口
			fprintf(js_file, "# Clearing only %s routes for independence\n", NET_ETH0);
			fprintf(js_file, "while route del default gw 0.0.0.0 dev %s 2>/dev/null ; do echo; done;\n", NET_ETH0);
		}
		// eth1网络
		else if (stricmp(if_name, NET_ETH1) EQU 0) {
			net = &g_pRunSets->eth1;
			is_eth1 = TRUE;

			// 只清除当前接口的旧路由，不影响其他接口
			fprintf(js_file, "# Clearing only %s routes for independence\n", NET_ETH1);
			fprintf(js_file, "while route del default gw 0.0.0.0 dev %s 2>/dev/null ; do echo; done;\n", NET_ETH1);
		}
		else {
			// 严格对应关系：不允许默认使用eth0配置，必须明确指定接口
			LOGE("Invalid interface name for network configuration: %s", if_name);
			fclose(js_file);
			return -1;
		}
	//	g_cur_if = if_name;

		// 只清理当前网口的相关进程
		fprintf(js_file, "# Kill processes specific to %s\n", if_name);
		fprintf(js_file, "pkill -f \"udhcpc.*%s\" 2>/dev/null || true\n", if_name);
		fprintf(js_file, "pkill -f \"net_scpt_%s\" 2>/dev/null || true\n", is_eth1 ? "eth1" : "eth0");

		// 杀掉ecm.sh
		fprintf(js_file, "killall -9 ecm.sh;\n");

		// 校时
		// fprintf(js_file, "killall -9 ntpd 1>/dev/null 2>&1\n");				// ntp
		fprintf(js_file, "killall -9 udhcpc 1>/dev/null 2>&1\n");			//	>/dev/null 2>&1
		fprintf(js_file, "rm %s\n", CFG_NETWORK("/tmp/", 0));

		LOGI("%s, if_name=%s, is_eth1=%d", __FUNCTION__, if_name, is_eth1);

		// 网络配置设置
		do {
			// 双网口模式下的处理
			
			// 差异化静态IP配置条件判定 (上网网卡判定是否设置了gateway)
			UINT8 must_static_ip = FALSE;
			if (!net->dhcp && strlen(net->ip) > 0 && strlen(net->netmask) > 0) {
				
			    if (stricmp(if_name, g_manual_gateway_interface) EQU 0) {
			    	// 上网网关接口：必须有完整配置（包括网关）    
			        if (strlen(net->gateway) > 0) {
			            must_static_ip = TRUE;
			        } 
			    } 
				else {
			        // 非上网接口：只需基础配置（不需要网关）
			        must_static_ip = TRUE;
			    }
			}

			LogW("if_name = %s, dhcp = %d, must_static_ip = %d", if_name, net->dhcp, must_static_ip);
			
			// IP地址, 子网掩码以及网关
			if (must_static_ip) {

				LOGI("specified %s ip[%s], netmask[%s], gateway[%s]", if_name, net->ip, net->netmask, net->gateway);
				fprintf(js_file, "ifconfig %s %s netmask %s\n", if_name, net->ip, net->netmask);

				// 单一网关逻辑：只有被选中的接口才设置默认网关
				if (strlen(net->gateway) > 0) {
					if (net_should_set_gateway(if_name)) {
						fprintf(js_file, "route add default gw %s dev %s 2>/dev/null\n", net->gateway, if_name);
						fprintf(js_file, "echo 'Default gateway set for %s: %s (ETH0 priority)'\n", if_name, net->gateway);
					} else {
						fprintf(js_file, "echo 'Gateway skipped for %s: %s (ETH0 priority - another interface will handle gateway)'\n", if_name, net->gateway);
					}
				}
				fprintf(js_file, "cp -f %s /tmp/\n", CFG_NETWORK(CFG_PATH, is_eth1));

				// DNS配置 - 使用追加模式避免覆盖其他接口的DNS
				{
					// 为了网口独立性，使用合并的DNS配置方式
					CHAR dns_content[1024] = {0};
					CHAR existing_dns[1024] = {0};

					// 读取现有的DNS配置
					FILE *existing_file = fopen("/tmp/resolv.conf", "rb");
					if (existing_file) {
						fread(existing_dns, 1, sizeof(existing_dns)-1, existing_file);
						fclose(existing_file);
					}

					// 构建当前接口的DNS配置
					if (net->dns[0][0]) {	// 有配置dns
						snprintf(dns_content, sizeof(dns_content), "# DNS for %s\nnameserver %s\n", if_name, net->dns[0]);
						if (net->dns[1][0]) {
							CHAR temp[256];
							snprintf(temp, sizeof(temp), "nameserver %s\n", net->dns[1]);
							strncat(dns_content, temp, sizeof(dns_content) - strlen(dns_content) - 1);
						}
					}
					else {		// 未配置dns，使用默认
						snprintf(dns_content, sizeof(dns_content),
							"# Default DNS for %s\n"
							"nameserver ***************\n"
							"nameserver 223.5.5.5\n"
							"nameserver *******\n", if_name);
					}

					// 合并DNS配置（避免重复）
					FILE *pfile = fopen("/tmp/resolv.conf", "wb");
					if (pfile) {
						// 写入现有的其他接口DNS配置（如果不冲突）
						if (strlen(existing_dns) > 0 && !strstr(existing_dns, dns_content)) {
							fprintf(pfile, "%s", existing_dns);
						}
						// 写入当前接口的DNS配置
						fprintf(pfile, "%s", dns_content);
						fflush(pfile);
						fclose(pfile);

						LOGI("DNS configured for %s independently", if_name);
					}
				}

			}
			else {

				char	dhcpc_cfg[64];

				LOGI("dhcp %s ", if_name);
				strcpy(dhcpc_cfg, "/opt/dhcpc.conf");

				// 简单的DHCP配置
				fprintf(js_file, "echo 'Starting DHCP for %s'\n", if_name);
				fprintf(js_file, "DHCP_GATEWAY_ALLOWED=1\n");

				// dhcp
				fprintf(js_file, "killall -9 udhcpc 1>/dev/null 2>&1\n");	// 不做这次可能有重复
#if defined(GB28181_ENABLE) && (GB_PLATFORM_ID==GB_PFID_SCDX_MJ)
				// 为兼容升级版本
				{
					fprintf(js_file, "cp -f /opt/dhcpc.conf /tmp/dhcpc.conf\n");
					fprintf(js_file, "cp -f /opt/app/dhcpc.conf /tmp/dhcpc.conf\n");
					fprintf(js_file, "sed -i 's#www.baidu.com#gateway.mj.sctel.com.cn#g' /tmp/dhcpc.conf\n");
					strcpy(dhcpc_cfg, "/tmp/dhcpc.conf");
				}
#endif
				// 创建带网关选择逻辑的DHCP脚本
				fprintf(js_file, "cat > /tmp/dhcpc_gw_%s.sh << 'EOF'\n", if_name);
				fprintf(js_file, "#!/bin/sh\n");
				fprintf(js_file, "case \"$1\" in\n");
				fprintf(js_file, "    deconfig)\n");
				fprintf(js_file, "        ifconfig $interface 0.0.0.0\n");
				fprintf(js_file, "        ;;\n");
				fprintf(js_file, "    bound|renew)\n");
				fprintf(js_file, "        ifconfig $interface $ip netmask $subnet\n");
				fprintf(js_file, "        # 单一网关逻辑：ETH0优先策略\n");
				fprintf(js_file, "        if [ -n \"$router\" ]; then\n");

				// 根据接口类型生成不同的网关设置逻辑（基于物理连接状态）
				if (stricmp(if_name, NET_ETH0) == 0) {
					// ETH0：只有当ETH0有物理连接时才设置网关
					fprintf(js_file, "            # Check ETH0 physical connection status\n");
					fprintf(js_file, "            if [ \"$(cat /sys/class/net/eth0/carrier 2>/dev/null)\" = \"1\" ]; then\n");
					fprintf(js_file, "                route add default gw $router dev $interface\n");
					fprintf(js_file, "                echo \"Gateway set for ETH0: $router (ETH0 has physical connection)\"\n");
					fprintf(js_file, "            else\n");
					fprintf(js_file, "                echo \"ETH0 has no physical connection, no gateway set\"\n");
					fprintf(js_file, "            fi\n");
				} else if (stricmp(if_name, NET_ETH1) == 0) {
					// ETH1：只有当ETH0没有物理连接时才设置网关
					fprintf(js_file, "            # Check ETH0 physical connection status for ETH1 gateway decision\n");
					fprintf(js_file, "            if [ \"$(cat /sys/class/net/eth0/carrier 2>/dev/null)\" != \"1\" ]; then\n");
					fprintf(js_file, "                route add default gw $router dev $interface\n");
					fprintf(js_file, "                echo \"Gateway set for ETH1: $router (ETH0 has no physical connection)\"\n");
					fprintf(js_file, "            else\n");
					fprintf(js_file, "                echo \"Gateway skipped for ETH1: $router (ETH0 has physical connection and priority)\"\n");
					fprintf(js_file, "            fi\n");
				}

				fprintf(js_file, "        fi\n");
				fprintf(js_file, "        # DNS优先级策略：与网关策略保持一致\n");
				fprintf(js_file, "        if [ -n \"$dns\" ]; then\n");

				// 根据接口类型生成不同的DNS设置逻辑，与网关逻辑保持一致（基于物理连接状态）
				if (stricmp(if_name, NET_ETH0) == 0) {
					// ETH0：只有当ETH0有物理连接时才设置DNS
					fprintf(js_file, "            # Check ETH0 physical connection status for DNS\n");
					fprintf(js_file, "            if [ \"$(cat /sys/class/net/eth0/carrier 2>/dev/null)\" = \"1\" ]; then\n");
					fprintf(js_file, "                echo \"# DNS configuration for ETH0 (ETH0 has physical connection)\" > /tmp/resolv.conf\n");
					fprintf(js_file, "                echo \"nameserver $dns\" >> /tmp/resolv.conf\n");
					fprintf(js_file, "                # 添加公共DNS作为备用\n");
					fprintf(js_file, "                echo \"nameserver *******\" >> /tmp/resolv.conf\n");
					fprintf(js_file, "                echo \"nameserver ***************\" >> /tmp/resolv.conf\n");
					fprintf(js_file, "                echo \"DNS set for ETH0: $dns (ETH0 has physical connection, with backup DNS)\"\n");
					fprintf(js_file, "            else\n");
					fprintf(js_file, "                echo \"ETH0 has no physical connection, no DNS set by ETH0\"\n");
					fprintf(js_file, "            fi\n");
				} else if (stricmp(if_name, NET_ETH1) == 0) {
					// ETH1：只有当ETH0没有物理连接时才设置DNS
					fprintf(js_file, "            # Check ETH0 physical connection status for ETH1 DNS decision\n");
					fprintf(js_file, "            if [ \"$(cat /sys/class/net/eth0/carrier 2>/dev/null)\" != \"1\" ]; then\n");
					fprintf(js_file, "                echo \"# DNS configuration for ETH1 (ETH0 has no physical connection)\" > /tmp/resolv.conf\n");
					fprintf(js_file, "                echo \"nameserver $dns\" >> /tmp/resolv.conf\n");
					fprintf(js_file, "                # 添加公共DNS作为备用\n");
					fprintf(js_file, "                echo \"nameserver *******\" >> /tmp/resolv.conf\n");
					fprintf(js_file, "                echo \"nameserver ***************\" >> /tmp/resolv.conf\n");
					fprintf(js_file, "                echo \"DNS set for ETH1: $dns (ETH0 has no physical connection, with backup DNS)\"\n");
					fprintf(js_file, "            else\n");
					fprintf(js_file, "                echo \"DNS skipped for ETH1: $dns (ETH0 has physical connection and priority)\"\n");
					fprintf(js_file, "            fi\n");
				}

				fprintf(js_file, "        fi\n");
				fprintf(js_file, "        ;;\n");
				fprintf(js_file, "esac\n");
				fprintf(js_file, "EOF\n");
				fprintf(js_file, "chmod +x /tmp/dhcpc_gw_%s.sh\n", if_name);

				// DHCP热插拔优化：启动前验证接口是否存在
				fprintf(js_file, "# DHCP client startup with interface validation\n");
				fprintf(js_file, "if [ -d \"/sys/class/net/%s\" ]; then\n", if_name);
				fprintf(js_file, "    echo \"Starting DHCP client for %s (interface exists)\"\n", if_name);

				// 使用自定义脚本启动DHCP客户端
				if (IS_VALID_ID(g_pEmbedInfo->p2pid))
				 	fprintf(js_file, "    DHCP_GATEWAY_ALLOWED=$DHCP_GATEWAY_ALLOWED udhcpc -t 10 -A 10 -b -i %s -s /tmp/dhcpc_gw_%s.sh -x hostname:IPC_%s\n",
				 		if_name, if_name, g_pEmbedInfo->p2pid);
				else
					fprintf(js_file, "    DHCP_GATEWAY_ALLOWED=$DHCP_GATEWAY_ALLOWED udhcpc -t 10 -A 10 -b -i %s -s /tmp/dhcpc_gw_%s.sh\n", if_name, if_name);

				fprintf(js_file, "else\n");
				fprintf(js_file, "    echo \"WARNING: Interface %s does not exist, skipping DHCP client startup\"\n", if_name);
				fprintf(js_file, "fi\n");
			}

		} while (0);
	}

	// 增加广播地址
	fprintf(js_file, "route add -host *************** dev %s\n", if_name);
	fprintf(js_file, "route add -net *************** netmask *************** %s\n", if_name);
	// 同步一次时间
	//fprintf(js_file, "ntpd -p %s -qNn&\n", TIME_CLOCK_SVR);

	// WiFi AP功能已移除，专注于双网口以太网模式

	// 网络独立性验证
	fprintf(js_file, "\n# Network independence verification for %s\n", if_name);
	fprintf(js_file, "echo '=== Network Configuration Summary for %s ==='\n", if_name);
	fprintf(js_file, "ip addr show %s | grep 'inet ' || echo 'No IP assigned to %s'\n", if_name, if_name);
	fprintf(js_file, "route -n | grep %s || echo 'No routes for %s'\n", if_name, if_name);

	// 显示网络配置状态
	fprintf(js_file, "echo '--- Network Configuration Status ---'\n");
	fprintf(js_file, "echo 'Interface: %s'\n", if_name);
	fprintf(js_file, "echo 'Configuration completed'\n");

	fprintf(js_file, "echo '=== End of %s Configuration ==='\n", if_name);

	// 验证网络连通性（非阻塞）
	fprintf(js_file, "(\n");
	fprintf(js_file, "  sleep 5\n");  // 等待配置生效
	fprintf(js_file, "  if ip addr show %s | grep -q 'inet '; then\n", if_name);
	fprintf(js_file, "    echo '%s network configuration successful'\n", if_name);
	fprintf(js_file, "    # 测试网络连通性（超时3秒）\n");
	fprintf(js_file, "    if timeout 3 ping -c 1 -I %s ******* >/dev/null 2>&1; then\n", if_name);
	fprintf(js_file, "      echo '%s network connectivity verified'\n", if_name);
	fprintf(js_file, "    else\n");
	fprintf(js_file, "      echo '%s network configured but connectivity test failed'\n", if_name);
	fprintf(js_file, "    fi\n");
	fprintf(js_file, "  else\n");
	fprintf(js_file, "    echo '%s network configuration failed - no IP assigned'\n", if_name);
	fprintf(js_file, "  fi\n");
	fprintf(js_file, ") &\n");  // 后台执行，不阻塞主流程

	// 网关冲突检测和修复机制
	// 简单的网络状态显示
	fprintf(js_file, "echo 'Network configuration completed for %s'\n", if_name);
	fprintf(js_file, "route -n | grep '^0.0.0.0' || echo 'No default gateway'\n");
	fprintf(js_file, "echo \"=== Gateway Management Complete ===\"\n");

	// onvif
	fprintf(js_file, "killall -9 onvif_server\n");
	if (g_pRunSets->misc_set.onvif_enable) {
		fprintf(js_file, "/opt/app/onvif_server > /dev/null 2>&1 &\n");
	}

	fclose(js_file);

	// 改变权限并运行
	set_wpa_ready(FALSE);
	system_no_fd("chmod 777 " JS_NET_FILE "; sleep 0.5; " JS_NET_FILE "&");

	LOGI("%s, done. if=%s, dual_eth_mode=%d", __FUNCTION__, if_name, dual_eth_mode);

	// WiFi配网功能已移除，专注于双网口以太网模式

	return OK;
}



static INT32		g_internet_state = INTERNET_STATE_UNKNOW-1;


/**
 * 域名转IP
 */
ULONG net_host_detect(LPCSTR host)
{
	ULONG 	dwIp = 0;
    INT32   fnRet;

	if (host EQU NULL  || strcmp(host, "") == 0)
		return 0;

	if (strcmp(host,"***************") == 0)
		return 0xFFFFFFFF;

	fnRet = inet_pton(AF_INET, host, &dwIp);
    if (fnRet <= 0) {

		struct addrinfo *result = NULL;
		struct addrinfo hints;

		bzero(&hints, sizeof (struct addrinfo));
		hints.ai_flags	= AI_CANONNAME; 	/* always return canonical name */
		hints.ai_family = AF_INET;			/* AF_UNSPEC, AF_INET, AF_INET6, etc. */
		hints.ai_socktype = SOCK_DGRAM;
		hints.ai_protocol = IPPROTO_UDP;

		if (getaddrinfo(host, NULL, &hints, &result) EQU 0)
		{
			struct addrinfo* res = result;

			/* prefer ip4 addresses */
			while (res)
			{
				if (res->ai_family == AF_INET) {
					dwIp = ((struct sockaddr_in*)(res->ai_addr))->sin_addr.s_addr;
					break;
				}
				res = res->ai_next;
			}

			freeaddrinfo(result);
		}


    }

	return dwIp;
}


// internet断网检测
LPVOID internet_abort_thread(LPVOID)
{
	SET_THREAD_NAME();
	LogI("running...");
	UINT32	reboot_time = NET_ABNORMAL_TIMEOUT_INTERNET_NEVER;
	INT32	reboot_countdown = reboot_time;
	INT32	inet_state = INTERNET_STATE_UNKNOW;
	INT32	delay_sec = 0;
	INT32	domain_pos = 0;
	UINT8	net_link_1_cnt = FALSE;
	LPCSTR 	domains[] = {
#if defined(GB28181_ENABLE) && (GB_PLATFORM_ID==GB_PFID_SCDX_MJ) 	// 魔镜平台
		"gateway.mj.sctel.com.cn",
		"open.mj.sctel.com.cn",
#endif
		"www.baidu.com",
		"www.qq.com",
		"www.google.com",
		"www.microsoft.com",
		"amazon.com",
		"vs98.com"
	};

	while (!system_quit() && !g_pRunSets->misc_set.disable_reboot) {
		if (inet_state != g_internet_state) {	// 状态改变
			inet_state = g_internet_state;
			if (g_internet_state >= INTERNET_STATE_SUCC) {
				// 连接上了
				reboot_time = NET_ABNORMAL_TIMEOUT_INTERNET_NEVER;
			}
			else if (g_internet_state EQU INTERNET_STATE_ABORT) {
				// 明确断开了
				reboot_time = NET_ABNORMAL_TIMEOUT_INTERNET;
			}
			else {
				// 未知状态
				reboot_time = NET_ABNORMAL_TIMEOUT_INTERNET_NEVER;
			}
			reboot_countdown = reboot_time;
			delay_sec = 0;
		}

		if (inet_state <= INTERNET_STATE_ABORT) {	// 中断或者未知
			do {
				if (delay_sec > 0) {	// 延时处理
					delay_sec--;
					break;
				}
				// WiFi配网模式已移除，双网口模式下无需此检查
				ULONG	uptm_first = get_app_uptime();
//				UINT32 	t_pkts[2], r_pkts[2], t_drop[2], r_drop[2], carr;
//
//				// 记得第一次网卡流量
//				net_dev_status(net_get_work_ifname(), &t_pkts[0], &r_pkts[0], &t_drop[0], &r_drop[0], &carr);

				/**
				 * 从未连接上internet的情况下:
				 *	step1: 先ping网关,
				 *	step2: 再连接有没有tcp连接
				 *		可能有nvr或者我们自己客户端或者vlc拉流,
				 * 		这种情况检测有没有tcp连接, 如果没有或者允许重启
				 */
				if (inet_state EQU INTERNET_STATE_UNKNOW) {
					UINT8 do_succ = FALSE;

#if defined(GB28181_ENABLE) && (GB_PLATFORM_ID==GB_PFID_SCDX_MJ)
					// 四川电信不做此判断,他们有时候测试就是不插网线
#else
					// 使用有线模式,并且网线没有插入,就是单机模式,也不重启(有些客户单机跑)
					if (!net_link_1_cnt
						&& stricmp(net_get_work_ifname(),  NET_ETH0) EQU 0
						&& !net_dev_carrier(NET_ETH0)) {
						reboot_countdown = reboot_time;
						delay_sec = 5;
						LogD("NET_ETH0 carrier=0, delay_sec=%d", delay_sec);
						break;
					}
#endif

					// ping网关
					{
						CHAR gw_ip[32];
						if (net_gw_addr(net_get_work_ifname(), gw_ip)){
							INT32 sock_icmp = socket(PF_INET, SOCK_RAW, IPPROTO_ICMP);

							if (sock_icmp != -1){
								fcntl(sock_icmp, F_SETFL, fcntl(sock_icmp,F_GETFL,0)|O_NONBLOCK);			// hcc add 设置为非阻塞

								// 网关可以ping
								if (do_ping(gw_ip, sock_icmp) EQU 0) {
									LogD("ping_ok gw_ip=%s",  gw_ip);
#if defined(GB28181_ENABLE) && (GB_PLATFORM_ID==GB_PFID_SCDX_MJ)
#elif defined(CMCC_HJQ)
#else
									// 中性版本,网络连接成功
									vsipc_set_state(VS_LED_NORMAL);
#endif
									do_succ = TRUE;
								}

								close(sock_icmp);
							}
						}
					}
					if (do_succ) {
						reboot_countdown = reboot_time;
						delay_sec = 10;
						net_link_1_cnt = TRUE;
						break;
					}

					// tcp传输无延时
					INT32 fnRet = net_tcp_jam();
					LogD("net_tcp_jam=%d",  fnRet);
					if (fnRet EQU 0) {
						reboot_countdown = reboot_time;
						delay_sec = 10;
						net_link_1_cnt = TRUE;
						break;
					}
				}
				else {
					/**
					 * 这种是曾经连接上了互联网后, 中途断开, 证明可以连接公网, 只需要使用域名来解析即可;
					 * 判断依据是只要能上公网, DNS必须是通的;
					 * 即使DNS不是通的, 他可以依靠自身的平台连接能力, 触发net_on_internet(1)来中止断网的判断,
					 * 如果他不使用这种方式来中止, 就证明他不通了
					 */
					UINT8 do_succ = FALSE;

					// 检测dns解析
					if (QfLength(domains) > 0) {
						if (domain_pos >= QfLength(domains))
							domain_pos = 0;
						//LogD("domain host=%s", domains[domain_pos]);
						ULONG haddr = net_host_detect(domains[domain_pos]);
						if (haddr != 0){
							LogD("domain_ok gw_ip=%s:%hhu.%hhu.%hhu.%hhu", domains[domain_pos],
								(haddr)&0xff, (haddr>>8)&0xff, (haddr>>16)&0xff, (haddr>>24)&0xff);
							do_succ = TRUE;
						}
						else {
							LogD("domain host=%s, errno=%d", domains[domain_pos], errno);
							domain_pos++;
						}
					}
					if (do_succ) {
						reboot_countdown = reboot_time;
						delay_sec = 10;
						net_link_1_cnt = TRUE;
						break;
					}
				}

				// 稍停2秒
				uptm_first = get_app_uptime() - uptm_first;
				if (uptm_first < 2) {
					Sleep((2-uptm_first)*1000);
					uptm_first = 2;
				}

//				// 记录第二次网卡流量(上面的dns与ping都会生网络包)
//				net_dev_status(net_get_work_ifname(), &t_pkts[1], &r_pkts[1], &t_drop[1], &r_drop[1], &carr);
//
//				UINT8 abnormal_status = (t_pkts[0] EQU t_pkts[1] && r_pkts[0] EQU r_pkts[1])
//										|| (r_drop[1] > r_drop[0]+2 || t_drop[1] > t_drop[0]+2);
//				LogD("abnormal_status=%hhu, reboot_countdown=%d",  abnormal_status, reboot_countdown);
//				if (!abnormal_status) {	// 网卡有流量交互, 配网时,重新计时
//					reboot_countdown = reboot_time;
//					delay_sec = 10;
//					break;
//				}

				reboot_countdown -= uptm_first;
				if (reboot_countdown <= 0) {	// 真正意义上重启
					LOGW("internet abort, system reboot!");
					set_except_flag();
					system_reboot();

					LOGI("%s() exit.", __FUNCTION__);

					END_THREAD;
				}
				LogD("inet_state=%d, reboot_countdown=%d",  inet_state, reboot_countdown);
			}while (FALSE);
		}
//		else {
//			if (QfLength(domains) > 0) {
//				if (domain_pos >= QfLength(domains))
//					domain_pos = 0;
//				LogD("domain host=%s", domains[domain_pos]);
//				ULONG haddr = net_host_detect(domains[domain_pos]);
//				if (haddr != 0){
//					LogD("domain_ok gw_ip=%s:%hhu.%hhu.%hhu.%hhu", domains[domain_pos],
//						(haddr)&0xff, (haddr>>8)&0xff, (haddr>>16)&0xff, (haddr>>24)&0xff);
//				}
//				else {
//					LogD("domain host=%s, errno=%d", domains[domain_pos], errno);
//					domain_pos++;
//				}
//			}
//		}

		Sleep(1000);
//		LogD("inet_state=%d", inet_state);
	}

	LOGI("%s() exit.", __FUNCTION__);

	END_THREAD;
}


/**
 * 连接internet
 * @param  state	=1则连接internet；=0则断开与internet连接, =-1则是启动的时候默认为internet未连接
 * @return         返回TRUE表示开启了时间同步, FALSE表示没有开启时间同步
 */
UINT8 net_on_internet(INT32 state)
{
#ifndef LT_DENGHONG
#ifdef CMCC_HJQ
	;;//和家亲不处理
#else
	vsipc_set_state(state EQU 1 ? VS_LED_NORMAL : VS_LED_NET_FAIL);
#endif
#endif

	LogI("cur_state=%d, new_state=%d", g_internet_state, state);
	if (g_internet_state != state) {
		static UINT8 s_internet_detect = FALSE;

		g_internet_state = state;
		if (!s_internet_detect && !g_pRunSets->misc_set.disable_reboot) {

#ifdef NET_ABNORMAL_REBOOT_IPC
		    if (PTHREAD_CREATE(NULL, NULL, internet_abort_thread, NULL)) {
		        LOGE("PTHREAD_CREATE(internet_abort_thread) = %d", errno);
			}
			else {
				s_internet_detect = TRUE;
			}
#endif
		}
	}

	// 上线互联网
	if (state EQU INTERNET_STATE_SUCC)
	{
		static UINT8	s_login = FALSE;

		if (!s_login) {
			s_login = TRUE;

			// 连接上网校时
			sync_time(TIME_CLOCK_SVR, FALSE);

			return TRUE;
		}
	}

	return FALSE;
}


/**
 * 判断是否成功连接INTERNET
 * @return  成功返回TRUE, 失败返回FALSE
 */
UINT8 net_internet_ready()
{
	return g_internet_state EQU INTERNET_STATE_SUCC ? TRUE:FALSE;
}


/**
 * 得到网络工作媒介
 * 返回: 0表示
 */
INT32 net_work_interface()
{
	return g_if_save_state;
}

/**
 * 得到工作网口名称
 */
LPCSTR net_get_work_ifname()
{
	if (strlen(g_cur_if_name) EQU 0)
		return NET_ETH0;

	return g_cur_if_name;
}

/**
 * 域名转IP
 */
ULONG net_host_2_addr(LPCSTR host)
{
	ULONG 	dwIp = 0;
    INT32   fnRet;

	if (host EQU NULL  || strcmp(host, "") == 0)
		return 0;

	if (strcmp(host,"***************") == 0)
		return 0xFFFFFFFF;

	fnRet = inet_pton(AF_INET, host, &dwIp);
    if (fnRet <= 0) {

		struct addrinfo *result = NULL;
		struct addrinfo hints;

		bzero(&hints, sizeof (struct addrinfo));
		hints.ai_flags	= AI_CANONNAME; 	/* always return canonical name */
		hints.ai_family = AF_INET;			/* AF_UNSPEC, AF_INET, AF_INET6, etc. */
		hints.ai_socktype = 0;				/* 0, SOCK_STREAM, SOCK_DGRAM, etc. */
//		hints.ai_family = AF_UNSPEC;		/* (在res->ai_family判断地址类型) */
		// 不论是tcp还是udp, 如果不通, 均是卡10秒返回
//		hints.ai_socktype = SOCK_STREAM;
//		hints.ai_protocol = IPPROTO_TCP;
//		hints.ai_socktype = SOCK_DGRAM;
//		hints.ai_protocol = IPPROTO_UDP;

		if (getaddrinfo(host, NULL, &hints, &result) EQU 0)
		{
			struct addrinfo* res = result;

			/* prefer ip4 addresses */
			while (res)
			{
				if (res->ai_family == AF_INET) {
					dwIp = ((struct sockaddr_in*)(res->ai_addr))->sin_addr.s_addr;
					break;
				}
				res = res->ai_next;
			}

			freeaddrinfo(result);
		}


    }

	return dwIp;
}


//=================================================================================================
// ping----begin
//=================================================================================================


//#define PING_OUT


u_int16_t Compute_cksum(struct icmp *pIcmp)
{
	u_int16_t *data = (u_int16_t *)pIcmp;
	int len = ICMP_LEN;
	u_int32_t sum = 0;

	while (len > 1)
	{
		sum += *data++;
		len -= 2;
	}
	if (1 == len)
	{
		u_int16_t tmp = *data;
		tmp &= 0xff00;
		sum += tmp;
	}

	//ICMP校验和带进位
	while (sum >> 16)
		sum = (sum >> 16) + (sum & 0x0000ffff);
	sum = ~sum;

	return sum;
}

void SetICMP(u_int16_t seq, char *SendBuffer)
{
	struct icmp *pIcmp;
	struct timeval *pTime;

	pIcmp = (struct icmp*)SendBuffer;

	/* 类型和代码分别为ICMP_ECHO,0代表请求回送 */
	pIcmp->icmp_type = ICMP_ECHO;
	pIcmp->icmp_code = 0;
	pIcmp->icmp_cksum = 0;							//校验和
	pIcmp->icmp_seq = seq;							//序号
	pIcmp->icmp_id = getpid();						//取进程号作为标志
	pTime = (struct timeval *)pIcmp->icmp_data;
	gettimeofday(pTime, NULL);						//数据段存放发送时间
	pIcmp->icmp_cksum = Compute_cksum(pIcmp);
}

int unpack(char *RecvBuffer)
{
	struct ip *Ip = (struct ip *)RecvBuffer;
	struct icmp *Icmp;
	int ipHeadLen;

	ipHeadLen = Ip->ip_hl << 2;	//ip_hl字段单位为4字节
	Icmp = (struct icmp *)(RecvBuffer + ipHeadLen);

	//判断接收到的报文是否是自己所发报文的响应
	if ((Icmp->icmp_type == ICMP_ECHOREPLY) && Icmp->icmp_id == getpid())
	{
		//struct timeval *SendTime = (struct timeval *)Icmp->icmp_data;

#ifdef PING_OUT
		printf("%u bytes from %s: icmp_seq=%u ttl=%u \n",
			ntohs(Ip->ip_len) - ipHeadLen,
			inet_ntoa(Ip->ip_src),
			Icmp->icmp_seq,
			Ip->ip_ttl);
#endif

		return PING_OK;
	}

	return PING_ERR;
}

int SendPacket(int sock_icmp, struct sockaddr_in *dest_addr, int *nSend, char *SendBuffer)
{
	memset(SendBuffer, 0, PING_BUFFER_SIZE);
	SetICMP(*nSend, SendBuffer);
	if (sendto(sock_icmp, SendBuffer, ICMP_LEN, 0,
		(struct sockaddr *)dest_addr, sizeof(struct sockaddr_in)) < 0)
	{
		perror("sendto");
		return PING_ERR;
	}
	return PING_OK;
}


int select_recvfrom(int fd, int sec)
{
	fd_set rset;
	struct timeval tv;

	FD_ZERO(&rset);
	FD_SET(fd, &rset);

	tv.tv_sec = sec;
	tv.tv_usec = 0;

	return (select(fd+1, &rset, NULL, NULL, &tv));
}

int RecvePacket(int sock_icmp, struct sockaddr_in *dest_addr, int *nRecv, char *RecvBuffer)
{
	UNUSED(nRecv);
	int RecvBytes = 0;
	int addrlen = sizeof(struct sockaddr_in);

	memset(RecvBuffer, 0, PING_BUFFER_SIZE);

	// 用select来recvfrom设置超时，因前面设备了sock_icmp为非阻塞，这里需要等待
	select_recvfrom(sock_icmp, PING_WAIT_TIME);

	if ((RecvBytes = recvfrom(sock_icmp, RecvBuffer, PING_BUFFER_SIZE,
			0, (struct sockaddr *)dest_addr, (socklen_t*)&addrlen)) < 0)
	{
		//perror("recvfrom");
		return PING_ERR;
	}

#if 1
	// 不处理回环
	if (unpack(RecvBuffer) < 0)
	{
		return PING_ERR;
	}
#endif

	return PING_OK;
}

//设置非阻塞
static void setnonblocking(int sockfd)
{
    int flag = fcntl(sockfd, F_GETFL, 0);
    if (flag < 0)
    {
        printf("fcntl F_GETFL fail\n");
        return;
    }
    if (fcntl(sockfd, F_SETFL, flag | O_NONBLOCK) < 0)
    {
        printf("fcntl F_SETFL fail\n");
    }
}

// ping功能入口函数；传入IP地址
// 成功返回0，失败返回-1
int do_ping(char *ip, int sock_icmp)
{
	in_addr_t inaddr;					//ip地址（网络字节序）
	struct sockaddr_in dest_addr; 		//IPv4专用socket地址,保存目的地址
	char RecvBuffer[PING_BUFFER_SIZE] = {0};
	char SendBuffer[PING_BUFFER_SIZE] = {0};
	int nRecv = 0;	//实际接收到的报文数
	int nSend = 0;
	int recv_err = 0;

	dest_addr.sin_family = AF_INET;

	/* 将点分十进制ip地址转换为网络字节序 */
	if ((inaddr = inet_addr(ip)) == INADDR_NONE)
	{
		dest_addr.sin_addr.s_addr = net_host_2_addr(ip);
		if (dest_addr.sin_addr.s_addr EQU 0)
			return -1;
	}
	else
	{
		memcpy(&dest_addr.sin_addr, &inaddr, sizeof(struct in_addr));
	}

#ifdef PING_OUT
	printf("PING %s", ip);
	printf("(%s) %d bytes of data.\n", inet_ntoa(dest_addr.sin_addr), ICMP_LEN);
#endif

	while (nSend <= PING_SEND_NUM)
	{
		int ret;

		if (SendPacket(sock_icmp, &dest_addr, &nSend, SendBuffer) < 0)
			return -1;
		else
			nSend++;

		// 如果为回环测试(127.0.0.1)，则重新获取；如果传输失败，返回-2
		ret = RecvePacket(sock_icmp, &dest_addr, &nRecv, RecvBuffer);
		if (ret < 0){
			recv_err++;

			if (recv_err > PING_SEND_NUM)
			{
#ifdef PING_OUT
				PRI_PING("ALL ERR", ip, nSend, nRecv);
#endif
				return PING_ERR;
			}
		}
		else			// 接收成功
		{
			nRecv++;
		}

		Sleep(1000);
	}


//	PRI_PING("ALL Succ", ip, nSend, nRecv);

	return PING_OK;
}


/**
 * 网络设备状态
 * @param  netif  网络设备名
 * @param  t_pkts  tx字节数
 * @param  r_pkts  rx字节数
 * @param  t_drop  tx掉包数
 * @param  r_drop  rx掉包数
 * @param  carr  插入次数
 * @return  成功TRUE;失败FALSE
 */
UINT8 net_dev_status(LPCSTR netif, 	UINT32 *t_pkts, UINT32 *r_pkts, UINT32 *t_drop, UINT32 *r_drop, UINT32 *carr)
{
	*t_pkts = 0;
	*r_pkts = 0;
	*t_drop = 0;
	*r_drop = 0;
	*carr = 1;

	if (netif EQU NULL)
		return FALSE;

	FILE *pfile = fopen("/proc/net/dev", "r");
	if (NULL != pfile) {
		LPSTR	line = NULL;
		size_t	nums = 0;

		while (getline(&line, &nums, pfile) != -1){
			if (strstr(line, netif)) {

				LPSTR 	str = strstr(line, netif)+strlen(netif)+1;

				if (sscanf(str, "%*s%u%*s%u%*s%*s%*s%*s%*s%u%*s%u", r_pkts, r_drop, t_pkts, t_drop) > 0) {
					//LOGW_NF("%u  %u  %u  %u", *r_pkts, *r_drop, *t_pkts, *t_drop);
				}

				break;
			}

			free(line);
			line = NULL;
			nums=0;

		}

		// 读取失败 也需要释放
		free(line);
		line = NULL;
		nums = 0;

		fclose(pfile);

	}
	else {
		return FALSE;
	}

	{
		char	file_name[128];

		sprintf(file_name, "/sys/class/net/%s/carrier_changes", netif);
		pfile = fopen(file_name, "r");
		if (NULL != pfile) {
			fscanf(pfile, "%d", carr);
			fclose(pfile);
		}
	}

	return TRUE;

}


float net_get_pktlossrate(char* netif)
{
	float pktlossrate = 0;
	UINT32 t_pkts, r_pkts, t_drop, r_drop, carr;

	if (net_dev_status(netif, &t_pkts, &r_pkts, &t_drop, &r_drop, &carr)) {

		LOGW_NF("%u  %u  %u  %u", r_pkts, r_drop, t_pkts, t_drop);
		pktlossrate = (r_drop + t_drop)*100.0/(r_pkts+t_pkts);
	}

	return pktlossrate;
}

/**
 * 判断网络设备ip是否冲突
 *  ip 检验的IP
 *  valid_num 包校验次数(1-10)
 *  time_out 超时时长（秒)
 *	if_name 如果if_name!=NULL,将指定网卡进行检测
 * @return   返回1表示可用；返回0表示请求失败 -1表示冲突
 */
INT32 net_ip_conflict_check(LPSTR ip, UINT32 valid_num, UINT32 time_out, LPCSTR if_name)
{
	CHAR cmd[128];
	CHAR res[32];
	int num = -1;
	INT32 check_res=0;

	do{
		if(ip EQU NULL)
		{
			LogE("system_ip_conflict_check() IP not exit!");
			break;
		}

		if(if_name != NULL && !net_dev_carrier(if_name))
		{
			LogE("system_ip_conflict_check() %s not ready!", if_name);
			break;
		}

		QfSet0(cmd,sizeof(cmd));
		sprintf(cmd,"arping -I %s -w %d -c %d %s | awk"
			" '/Received/ {num=$2;print num}'|tr -d '\n'",
			(if_name EQU NULL)?net_get_work_ifname():if_name, QF_MAX(2,QF_MIN(valid_num,10)), QF_MAX(2,QF_MIN(time_out,10)), ip);
		LogI("ip check:/n%s/n", cmd);
		FILE *fp = popen(cmd, "r");
		if(fp)
		{
			QfSet0(res,sizeof(res));
			fread(res, 1, sizeof(res) - 1, fp);
			pclose(fp);
			LOGI("recv:%s", res);
    		if(sscanf(res, "%d", &num))
			{	//有回复,表示IP不可用
    			if(num EQU valid_num)
    			{
    				check_res = -1;
    			}else if(num EQU 0){
    				check_res = 1;
				}
    		}
		}else
		{
			LOGE("popen %s error",cmd);
		}

	}while(FALSE);

	return check_res;
}



int get_wlan0_quality(char* netif)
{
	int quality = 0;

	FILE *pfile = fopen("/proc/net/wireless", "r");
	if (NULL != pfile) {
		char	*line = NULL;
		size_t	nums = 0;

		while (getline(&line, &nums, pfile) != -1){

			if (strstr(line, netif)) {

				char link[32];
				char* str = strstr(line, netif)+strlen(netif)+1;

				if (sscanf(str, "%*s%*s%s",  link) > 0) {

					LOGW_NF("%d\n", atoi(link));
					quality = atoi(link);  // 返回信号强度
				}

				break;
			}

			free(line);
			line = NULL;
			nums=0;

		}

		// 读取失败 也需要释放
		free(line);
		line = NULL;
		fclose(pfile);

	}

	return quality;
}











//=================================================================================================
// ping----end
//=================================================================================================

